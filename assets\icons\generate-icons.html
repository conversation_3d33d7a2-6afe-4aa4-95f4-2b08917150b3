<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        canvas {
            border: 1px solid #ccc;
            margin-bottom: 10px;
        }
        button {
            background: #0056b3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0041a3;
        }
        .download-all {
            background: #28a745;
            font-size: 16px;
            padding: 15px 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MBBS Vostrix PWA Icon Generator</h1>
        <p>This tool generates all required PWA icons for the MBBS Vostrix website.</p>
        
        <button class="download-all" onclick="generateAllIcons()">Generate All Icons</button>
        
        <div class="icon-preview" id="iconPreview">
            <!-- Icons will be generated here -->
        </div>
    </div>

    <script>
        const iconSizes = [
            { size: 16, name: 'icon-16.png' },
            { size: 32, name: 'icon-32.png' },
            { size: 72, name: 'icon-72.png' },
            { size: 96, name: 'icon-96.png' },
            { size: 128, name: 'icon-128.png' },
            { size: 144, name: 'icon-144.png' },
            { size: 152, name: 'icon-152.png' },
            { size: 192, name: 'icon-192.png' },
            { size: 384, name: 'icon-384.png' },
            { size: 512, name: 'icon-512.png' }
        ];

        const maskableIconSizes = [
            { size: 192, name: 'maskable-icon-192.png' },
            { size: 512, name: 'maskable-icon-512.png' }
        ];

        const shortcutIcons = [
            { size: 96, name: 'apply-icon-96.png' },
            { size: 96, name: 'program-icon-96.png' },
            { size: 96, name: 'campus-icon-96.png' }
        ];

        function createIcon(size, text, bgColor = '#0056b3', textColor = '#ffffff', isMaskable = false) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = size;
            canvas.height = size;
            
            // Background
            if (isMaskable) {
                // Maskable icons need safe zone
                const safeZone = size * 0.8;
                const offset = (size - safeZone) / 2;
                
                ctx.fillStyle = bgColor;
                ctx.fillRect(0, 0, size, size);
                
                ctx.fillStyle = textColor;
                ctx.fillRect(offset, offset, safeZone, safeZone);
                
                ctx.fillStyle = bgColor;
            } else {
                ctx.fillStyle = bgColor;
                ctx.fillRect(0, 0, size, size);
                ctx.fillStyle = textColor;
            }
            
            // Text
            const fontSize = Math.max(size / 8, 12);
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            const lines = text.split('\n');
            const lineHeight = fontSize * 1.2;
            const totalHeight = lines.length * lineHeight;
            const startY = (size / 2) - (totalHeight / 2) + (lineHeight / 2);
            
            lines.forEach((line, index) => {
                ctx.fillText(line, size / 2, startY + (index * lineHeight));
            });
            
            return canvas;
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }

        function generateAllIcons() {
            const preview = document.getElementById('iconPreview');
            preview.innerHTML = '';
            
            // Generate regular icons
            iconSizes.forEach(icon => {
                const canvas = createIcon(icon.size, 'MBBS\nVostrix');
                const item = createIconItem(canvas, icon.name, icon.size);
                preview.appendChild(item);
            });
            
            // Generate maskable icons
            maskableIconSizes.forEach(icon => {
                const canvas = createIcon(icon.size, 'MV', '#0056b3', '#ffffff', true);
                const item = createIconItem(canvas, icon.name, icon.size);
                preview.appendChild(item);
            });
            
            // Generate shortcut icons
            const shortcutTexts = ['Apply', 'Program', 'Campus'];
            const shortcutColors = ['#28a745', '#ffc107', '#17a2b8'];
            
            shortcutIcons.forEach((icon, index) => {
                const canvas = createIcon(icon.size, shortcutTexts[index], shortcutColors[index]);
                const item = createIconItem(canvas, icon.name, icon.size);
                preview.appendChild(item);
            });
            
            // Generate favicon
            const faviconCanvas = createIcon(32, 'MV');
            const faviconItem = createIconItem(faviconCanvas, 'favicon.ico', 32);
            preview.appendChild(faviconItem);
            
            // Generate social media images
            generateSocialImages();
        }

        function createIconItem(canvas, filename, size) {
            const item = document.createElement('div');
            item.className = 'icon-item';
            
            const canvasClone = canvas.cloneNode(true);
            canvasClone.style.width = '64px';
            canvasClone.style.height = '64px';
            
            const label = document.createElement('div');
            label.textContent = `${filename} (${size}x${size})`;
            
            const downloadBtn = document.createElement('button');
            downloadBtn.textContent = 'Download';
            downloadBtn.onclick = () => downloadCanvas(canvas, filename);
            
            item.appendChild(canvasClone);
            item.appendChild(label);
            item.appendChild(downloadBtn);
            
            return item;
        }

        function generateSocialImages() {
            // Generate OG image (1200x630)
            const ogCanvas = document.createElement('canvas');
            ogCanvas.width = 1200;
            ogCanvas.height = 630;
            const ogCtx = ogCanvas.getContext('2d');
            
            // Background gradient
            const gradient = ogCtx.createLinearGradient(0, 0, 1200, 630);
            gradient.addColorStop(0, '#0056b3');
            gradient.addColorStop(1, '#0041a3');
            ogCtx.fillStyle = gradient;
            ogCtx.fillRect(0, 0, 1200, 630);
            
            // Text
            ogCtx.fillStyle = '#ffffff';
            ogCtx.font = 'bold 72px Arial';
            ogCtx.textAlign = 'center';
            ogCtx.fillText('MBBS Vostrix', 600, 250);
            
            ogCtx.font = '36px Arial';
            ogCtx.fillText('Study Medicine at FEFU, Russia', 600, 320);
            
            ogCtx.font = '24px Arial';
            ogCtx.fillText('WHO & NMC Recognized • English Medium • Affordable Fees', 600, 380);
            
            const ogItem = createIconItem(ogCanvas, 'og-image.jpg', '1200x630');
            document.getElementById('iconPreview').appendChild(ogItem);
            
            // Generate Twitter image (1200x600)
            const twitterCanvas = document.createElement('canvas');
            twitterCanvas.width = 1200;
            twitterCanvas.height = 600;
            const twitterCtx = twitterCanvas.getContext('2d');
            
            // Copy OG image content
            twitterCtx.drawImage(ogCanvas, 0, 0, 1200, 630, 0, 0, 1200, 600);
            
            const twitterItem = createIconItem(twitterCanvas, 'twitter-image.jpg', '1200x600');
            document.getElementById('iconPreview').appendChild(twitterItem);
        }

        // Auto-generate on page load
        window.onload = generateAllIcons;
    </script>
</body>
</html>
