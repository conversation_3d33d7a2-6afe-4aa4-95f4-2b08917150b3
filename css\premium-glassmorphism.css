/* ===== PREMIUM GLASSMORPHISM DESIGN SYSTEM ===== */
/* Medical education themed glassmorphism effects with accessibility support */

/* ===== BASE GLASSMORPHISM CLASSES ===== */
.glass-effect {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.glass-effect-strong {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-backdrop-strong);
  -webkit-backdrop-filter: var(--glass-backdrop-strong);
  border: 1px solid var(--glass-border-strong);
  box-shadow: var(--shadow-multi-layer);
}

.glass-effect-subtle {
  background: var(--glass-bg-subtle);
  backdrop-filter: var(--glass-backdrop-subtle);
  -webkit-backdrop-filter: var(--glass-backdrop-subtle);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-premium);
}

/* ===== FALLBACK FOR UNSUPPORTED BROWSERS ===== */
@supports not (backdrop-filter: blur(20px)) {
  .glass-effect,
  .glass-effect-strong,
  .glass-effect-subtle {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  .info-card.glass-effect,
  .testimonial-card.glass-effect,
  .application-form.glass-effect,
  .cost-calculator-section.glass-effect {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
  }
}

/* ===== INFO CARDS GLASSMORPHISM ===== */
.info-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-multi-layer);
  border-radius: var(--radius-2xl);
  transition: all var(--duration-300) var(--ease-premium);
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--duration-700) var(--ease-premium);
  z-index: 1;
}

.info-card:hover::before {
  left: 100%;
}

.info-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-multi-layer-hover);
  border-color: var(--glass-border-strong);
  background: var(--glass-bg-strong);
}

/* ===== TESTIMONIAL CARDS GLASSMORPHISM ===== */
.testimonial-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-multi-layer);
  border-radius: var(--radius-2xl);
  transition: all var(--duration-300) var(--ease-premium);
  position: relative;
  overflow: hidden;
}

.testimonial-card::after {
  content: '"';
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  font-size: var(--text-6xl);
  font-family: serif;
  color: rgba(37, 99, 235, 0.1);
  line-height: 1;
  z-index: 1;
}

.testimonial-card:hover {
  transform: scale(1.03);
  box-shadow: var(--shadow-multi-layer-hover);
  background: var(--glass-bg-strong);
}

/* ===== APPLICATION FORM GLASSMORPHISM ===== */
.application-form {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-multi-layer);
  border-radius: var(--radius-3xl);
  position: relative;
  overflow: hidden;
}

.application-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-medical);
  z-index: 2;
}

/* ===== COST CALCULATOR GLASSMORPHISM ===== */
.cost-calculator-section {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-backdrop-strong);
  -webkit-backdrop-filter: var(--glass-backdrop-strong);
  border: 1px solid var(--glass-border-strong);
  box-shadow: var(--shadow-multi-layer);
  border-radius: var(--radius-3xl);
  position: relative;
  overflow: hidden;
}

.calculator-container {
  position: relative;
  z-index: 2;
}

.calculator-results {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop-subtle);
  -webkit-backdrop-filter: var(--glass-backdrop-subtle);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-premium);
  border-radius: var(--radius-2xl);
}

/* ===== SAFETY CARDS GLASSMORPHISM ===== */
.safety-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-multi-layer);
  border-radius: var(--radius-2xl);
  transition: all var(--duration-300) var(--ease-premium);
  position: relative;
  overflow: hidden;
}

.safety-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-multi-layer-hover);
  background: var(--glass-bg-strong);
}

/* ===== ABOUT CARDS GLASSMORPHISM ===== */
.about-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-multi-layer);
  border-radius: var(--radius-2xl);
  transition: all var(--duration-300) var(--ease-premium);
  position: relative;
  overflow: hidden;
}

.about-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: var(--shadow-multi-layer-hover);
  background: var(--glass-bg-strong);
}

/* ===== NAVIGATION GLASSMORPHISM ===== */
.header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--duration-300) cubic-bezier(0.4, 0, 0.2, 1);
}

.header.scrolled {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  box-shadow: var(--shadow-lg);
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .glass-effect,
  .info-card,
  .testimonial-card,
  .application-form,
  .cost-calculator-section,
  .safety-card,
  .about-card {
    background: var(--glass-bg-dark);
    border-color: var(--glass-border-dark);
    box-shadow: var(--glass-shadow-dark);
  }
  
  .header {
    background: rgba(0, 0, 0, 0.8);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .header.scrolled {
    background: rgba(0, 0, 0, 0.95);
  }
}

/* ===== ACCESSIBILITY SUPPORT ===== */
@media (prefers-reduced-motion: reduce) {
  .info-card,
  .testimonial-card,
  .safety-card,
  .about-card {
    transition: none;
  }
  
  .info-card::before {
    display: none;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .glass-effect,
  .info-card,
  .testimonial-card,
  .application-form,
  .cost-calculator-section,
  .safety-card,
  .about-card {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.8);
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
}

/* ===== MOBILE OPTIMIZATIONS ===== */
@media (max-width: 768px) {
  .glass-effect,
  .info-card,
  .testimonial-card,
  .application-form,
  .cost-calculator-section,
  .safety-card,
  .about-card {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }
  
  .info-card:hover,
  .testimonial-card:hover,
  .safety-card:hover,
  .about-card:hover {
    transform: translateY(-2px) scale(1.01);
  }
}

/* ===== BROWSER COMPATIBILITY ===== */
/* Firefox fallback */
@-moz-document url-prefix() {
  .glass-effect,
  .info-card,
  .testimonial-card,
  .application-form,
  .cost-calculator-section,
  .safety-card,
  .about-card {
    background: rgba(255, 255, 255, 0.9);
  }
}

/* Edge legacy fallback */
@supports (-ms-ime-align: auto) {
  .glass-effect,
  .info-card,
  .testimonial-card,
  .application-form,
  .cost-calculator-section,
  .safety-card,
  .about-card {
    background: rgba(255, 255, 255, 0.9);
  }
}
