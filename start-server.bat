@echo off
echo Starting local development server for MBBS Vostrix...
echo.
echo Choose your preferred method:
echo 1. Python 3 (if installed)
echo 2. Node.js (if installed)
echo 3. PHP (if installed)
echo.

:choice
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto python
if "%choice%"=="2" goto node
if "%choice%"=="3" goto php
echo Invalid choice. Please enter 1, 2, or 3.
goto choice

:python
echo Starting Python server...
python -m http.server 8000
if errorlevel 1 (
    echo Python 3 not found. Trying Python 2...
    python -m SimpleHTTPServer 8000
)
goto end

:node
echo Starting Node.js server...
npx http-server -p 8000 -c-1
if errorlevel 1 (
    echo Node.js or http-server not found.
    echo Install with: npm install -g http-server
    pause
)
goto end

:php
echo Starting PHP server...
php -S localhost:8000
if errorlevel 1 (
    echo PHP not found.
    pause
)
goto end

:end
echo.
echo Server should be running at: http://localhost:8000
echo Press Ctrl+C to stop the server
pause
