/* ===== PREMIUM MEDICAL EDUCATION ANIMATIONS ===== */
/* Sophisticated animations for high-end user experience */

/* ===== PREMIUM CARD ANIMATIONS ===== */
.premium-card {
  position: relative;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  transition: all var(--duration-500) var(--ease-premium);
  overflow: hidden;
}

.premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--duration-700) var(--ease-premium);
}

.premium-card:hover::before {
  left: 100%;
}

.premium-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-premium);
  border-color: var(--primary-300);
}

/* ===== FLOATING ELEMENTS ===== */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    filter: brightness(1);
  }
  33% {
    transform: translateY(-15px) rotate(1deg);
    filter: brightness(1.1);
  }
  66% {
    transform: translateY(-8px) rotate(-1deg);
    filter: brightness(1.05);
  }
}

@keyframes floatMedical {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-12px) translateX(3px) rotate(2deg);
  }
  50% {
    transform: translateY(-5px) translateX(-2px) rotate(-1deg);
  }
  75% {
    transform: translateY(-8px) translateX(1px) rotate(1deg);
  }
}

.floating-element {
  animation: float 6s ease-in-out infinite;
  will-change: transform;
  contain: layout style paint;
}

.floating-medical {
  animation: floatMedical 8s ease-in-out infinite;
  will-change: transform;
  contain: layout style paint;
}

/* Staggered floating for multiple elements */
.floating-element:nth-child(1) { animation-delay: 0s; }
.floating-element:nth-child(2) { animation-delay: 1s; }
.floating-element:nth-child(3) { animation-delay: 2s; }
.floating-element:nth-child(4) { animation-delay: 3s; }
.floating-element:nth-child(5) { animation-delay: 4s; }

/* ===== PREMIUM BUTTON ANIMATIONS ===== */
.premium-button {
  position: relative;
  background: linear-gradient(135deg, #2563eb, #0891b2);
  border: none;
  border-radius: var(--radius-full);
  color: white;
  font-weight: var(--font-semibold);
  padding: var(--space-4) var(--space-8);
  cursor: pointer;
  overflow: hidden;
  transition: all var(--duration-300) var(--ease-premium);
  box-shadow: var(--shadow-button-premium);
  will-change: transform, box-shadow;
  contain: layout style paint;
  min-height: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

/* Shimmer effect */
.premium-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left var(--duration-700) var(--ease-premium);
  z-index: 1;
}

.premium-button:hover::before {
  left: 100%;
}

/* Ripple effect container */
.premium-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width var(--duration-500) var(--ease-elastic), height var(--duration-500) var(--ease-elastic);
  z-index: 1;
}

.premium-button:hover::after {
  width: 300px;
  height: 300px;
}

.premium-button:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-button-hover);
}

.premium-button:active {
  transform: translateY(-2px) scale(1.02);
}

/* Button content positioning */
.premium-button > * {
  position: relative;
  z-index: 2;
}

/* ===== CTA BUTTON ENHANCEMENT ===== */
.cta-button {
  position: relative;
  overflow: hidden;
  transition: all var(--duration-300) var(--ease-premium);
  will-change: transform, box-shadow;
  contain: layout style paint;
}

.cta-button.primary {
  background: linear-gradient(135deg, #2563eb, #0891b2);
  box-shadow: var(--shadow-button-premium);
}

.cta-button.primary:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-button-hover);
}

/* ===== APPLY BUTTON ENHANCEMENT ===== */
.apply-button {
  background: linear-gradient(135deg, #2563eb, #0891b2);
  position: relative;
  overflow: hidden;
  transition: all var(--duration-300) var(--ease-premium);
  box-shadow: var(--shadow-button-premium);
  will-change: transform, box-shadow;
}

.apply-button:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-button-hover);
}

/* ===== DOWNLOAD BROCHURE BUTTON ===== */
.download-brochure-btn {
  background: linear-gradient(135deg, #2563eb, #0891b2);
  position: relative;
  overflow: hidden;
  transition: all var(--duration-300) var(--ease-premium);
  box-shadow: var(--shadow-button-premium);
  will-change: transform, box-shadow;
}

.download-brochure-btn:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-button-hover);
}

/* ===== SCROLL REVEAL ANIMATIONS ===== */
.reveal-up {
  opacity: 0;
  transform: translateY(60px);
  transition: all var(--duration-700) var(--ease-premium);
}

.reveal-up.revealed {
  opacity: 1;
  transform: translateY(0);
}

.reveal-left {
  opacity: 0;
  transform: translateX(-60px);
  transition: all var(--duration-700) var(--ease-premium);
}

.reveal-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.reveal-right {
  opacity: 0;
  transform: translateX(60px);
  transition: all var(--duration-700) var(--ease-premium);
}

.reveal-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

.reveal-scale {
  opacity: 0;
  transform: scale(0.8);
  transition: all var(--duration-700) var(--ease-bounce);
}

.reveal-scale.revealed {
  opacity: 1;
  transform: scale(1);
}

/* ===== PREMIUM LOADING ANIMATIONS ===== */
.premium-loader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
}

/* ===== DUAL RING SPINNER ===== */
.medical-pulse {
  position: relative;
  width: 80px;
  height: 80px;
}

.medical-pulse::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 4px solid transparent;
  border-top: 4px solid var(--primary-600);
  border-radius: 50%;
  animation: spinClockwise 2s linear infinite;
}

.medical-pulse::after {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  border: 3px solid transparent;
  border-bottom: 3px solid var(--secondary-500);
  border-radius: 50%;
  animation: spinCounterClockwise 1.5s linear infinite;
}

@keyframes spinClockwise {
  0% {
    transform: rotate(0deg);
    border-top-color: var(--primary-600);
  }
  33% {
    border-top-color: var(--success-500);
  }
  66% {
    border-top-color: var(--secondary-500);
  }
  100% {
    transform: rotate(360deg);
    border-top-color: var(--primary-600);
  }
}

@keyframes spinCounterClockwise {
  0% {
    transform: rotate(0deg);
    border-bottom-color: var(--secondary-500);
  }
  33% {
    border-bottom-color: var(--primary-600);
  }
  66% {
    border-bottom-color: var(--success-500);
  }
  100% {
    transform: rotate(-360deg);
    border-bottom-color: var(--secondary-500);
  }
}

/* ===== GLASSMORPHISM LOADING CONTAINER ===== */
.premium-loader {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop-strong);
  -webkit-backdrop-filter: var(--glass-backdrop-strong);
  border: 1px solid var(--glass-border);
}

.loading-content {
  background: var(--glass-bg-subtle);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  border: 1px solid var(--glass-border);
}

/* ===== COLOR TRANSITION LOADING ===== */
@keyframes colorTransition {
  0% {
    background: linear-gradient(135deg, #2563eb, #2563eb);
  }
  50% {
    background: linear-gradient(135deg, #2563eb, #f59e0b);
  }
  100% {
    background: linear-gradient(135deg, #f59e0b, #2563eb);
  }
}

.color-transition-loader {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  animation: colorTransition 3s ease-in-out infinite;
  margin: 0 auto var(--space-4);
}

/* ===== HEARTBEAT ANIMATION ===== */
.heartbeat {
  animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  14% { transform: scale(1.1); }
  28% { transform: scale(1); }
  42% { transform: scale(1.1); }
  70% { transform: scale(1); }
}

/* ===== MAGNETIC HOVER EFFECTS ===== */
.magnetic-hover {
  transition: transform var(--duration-300) var(--ease-premium);
  cursor: pointer;
  will-change: transform;
  contain: layout style paint;
}

.magnetic-hover:hover {
  transform: translateY(-4px);
}

/* Enhanced magnetic effect with JavaScript interaction */
.magnetic-active {
  transition: transform var(--duration-150) var(--ease-premium);
}

.glow-on-hover {
  transition: all var(--duration-300) var(--ease-premium);
  will-change: transform, box-shadow;
}

.glow-on-hover:hover {
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

/* ===== UPWARD FLOWING PARTICLES ===== */
@keyframes particleFlow {
  0% {
    transform: translateY(100vh) translateX(0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 0.8;
    transform: translateY(90vh) translateX(10px) scale(0.5);
  }
  50% {
    opacity: 1;
    transform: translateY(50vh) translateX(-5px) scale(1);
  }
  90% {
    opacity: 0.8;
    transform: translateY(10vh) translateX(15px) scale(0.8);
  }
  100% {
    transform: translateY(-10vh) translateX(20px) scale(0);
    opacity: 0;
  }
}

.particle-medical {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--primary-400);
  border-radius: 50%;
  animation: particleFlow 12s linear infinite;
  will-change: transform, opacity;
  contain: layout style paint;
}

.particle-medical:nth-child(odd) {
  background: var(--success-400);
  animation-duration: 15s;
}

.particle-medical:nth-child(3n) {
  background: var(--secondary-400);
  animation-duration: 10s;
}

/* Staggered particle delays */
.particle-medical:nth-child(1) { animation-delay: 0s; left: 10%; }
.particle-medical:nth-child(2) { animation-delay: 2s; left: 20%; }
.particle-medical:nth-child(3) { animation-delay: 4s; left: 30%; }
.particle-medical:nth-child(4) { animation-delay: 6s; left: 40%; }
.particle-medical:nth-child(5) { animation-delay: 8s; left: 50%; }
.particle-medical:nth-child(6) { animation-delay: 10s; left: 60%; }
.particle-medical:nth-child(7) { animation-delay: 12s; left: 70%; }
.particle-medical:nth-child(8) { animation-delay: 14s; left: 80%; }
.particle-medical:nth-child(9) { animation-delay: 16s; left: 90%; }

/* ===== STAGGERED ANIMATIONS ===== */
.stagger-item {
  opacity: 0;
  transform: translateY(30px);
  transition: all var(--duration-500) var(--ease-premium);
}

.stagger-item.animate {
  opacity: 1;
  transform: translateY(0);
}

.stagger-item:nth-child(1) { transition-delay: 0ms; }
.stagger-item:nth-child(2) { transition-delay: 100ms; }
.stagger-item:nth-child(3) { transition-delay: 200ms; }
.stagger-item:nth-child(4) { transition-delay: 300ms; }
.stagger-item:nth-child(5) { transition-delay: 400ms; }
.stagger-item:nth-child(6) { transition-delay: 500ms; }

/* ===== MORPHING SHAPES ===== */
.morphing-shape {
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  animation: morph 8s ease-in-out infinite;
}

@keyframes morph {
  0%, 100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
  25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
  50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; }
  75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; }
}

/* ===== PREMIUM TEXT ANIMATIONS ===== */
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--primary-600);
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--primary-600); }
}

.gradient-text {
  background: var(--gradient-medical);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* ===== PARTICLE EFFECTS ===== */
.particle-container {
  position: relative;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--primary-400);
  border-radius: 50%;
  opacity: 0.7;
  animation: float-particle 6s linear infinite;
}

@keyframes float-particle {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 0.7;
  }
  90% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-100px) translateX(100px);
    opacity: 0;
  }
}

/* ===== RESPONSIVE PREMIUM ANIMATIONS ===== */
@media (max-width: 768px) {
  .premium-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
  
  .reveal-up, .reveal-left, .reveal-right {
    transform: translateY(30px);
  }
  
  .reveal-up.revealed, .reveal-left.revealed, .reveal-right.revealed {
    transform: translateY(0);
  }
}

/* ===== PREMIUM FORM STYLING ===== */
.premium-input-wrapper {
  position: relative;
  margin-bottom: var(--space-6);
}

.premium-input-wrapper input,
.premium-input-wrapper textarea,
.premium-input-wrapper select {
  width: 100%;
  padding: var(--space-4) var(--space-6);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-xl);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  font-size: var(--text-base);
  transition: all var(--duration-300) var(--ease-premium);
  outline: none;
}

.premium-input-wrapper.focused input,
.premium-input-wrapper.focused textarea,
.premium-input-wrapper.focused select {
  border-color: var(--primary-500);
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

.premium-input-wrapper.valid input,
.premium-input-wrapper.valid textarea,
.premium-input-wrapper.valid select {
  border-color: var(--success-500);
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

.premium-input-wrapper.invalid input,
.premium-input-wrapper.invalid textarea,
.premium-input-wrapper.invalid select {
  border-color: var(--error-500);
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* ===== RIPPLE EFFECT ===== */
.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* ===== PREMIUM LOADING SCREEN ===== */
.premium-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity var(--duration-500) var(--ease-premium);
}

.loading-content {
  text-align: center;
  color: var(--color-text-primary);
}

.loading-content h3 {
  margin-bottom: var(--space-6);
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
}

.progress-bar {
  width: 300px;
  height: 8px;
  background: var(--color-border);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin: var(--space-4) 0;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-medical);
  border-radius: var(--radius-full);
  transition: width var(--duration-300) var(--ease-premium);
  width: 0%;
}

.loading-percentage {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--primary-600);
}

@media (prefers-reduced-motion: reduce) {
  .premium-card, .premium-button, .reveal-up, .reveal-left, .reveal-right, .reveal-scale {
    transition: none;
    animation: none;
  }

  .floating-element, .heartbeat, .morphing-shape {
    animation: none;
  }
}
