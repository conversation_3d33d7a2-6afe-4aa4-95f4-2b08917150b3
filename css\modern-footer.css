/* ===== MODERN FOOTER STYLES ===== */

/* Contact Link Styles */
.contact-link {
  color: inherit;
  text-decoration: none;
  transition: var(--transition-default);
  border-bottom: 1px solid transparent;
}

.contact-link:hover,
.contact-link:focus {
  color: var(--secondary-400);
  border-bottom-color: var(--secondary-400);
  text-decoration: none;
}

.whatsapp-link:hover,
.whatsapp-link:focus {
  color: #25d366;
  border-bottom-color: #25d366;
}

.emergency-contact {
  color: var(--error-400);
  font-weight: var(--font-semibold);
}
/* Professional Footer Design for Medical Education Platform */

footer {
  background: linear-gradient(135deg, var(--gray-900), var(--gray-800));
  color: var(--gray-100);
  position: relative;
  overflow: hidden;
}

footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="medical-pattern" width="100" height="100" patternUnits="userSpaceOnUse"><path d="M50 10 L50 40 M35 25 L65 25" stroke="%23374151" stroke-width="2" fill="none" opacity="0.1"/><circle cx="50" cy="75" r="8" stroke="%23374151" stroke-width="2" fill="none" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23medical-pattern)"/></svg>');
  opacity: 0.3;
}

.footer-content {
  max-width: var(--container-7xl);
  margin: 0 auto;
  padding: var(--space-20) var(--space-6) var(--space-12);
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--space-12);
  position: relative;
  z-index: 2;
}

.footer-section h3 {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: white;
  margin-bottom: var(--space-6);
  position: relative;
}

.footer-section h3::after {
  content: '';
  position: absolute;
  bottom: -var(--space-2);
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  border-radius: var(--radius-full);
}

.footer-section h4 {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--gray-200);
  margin-bottom: var(--space-4);
}

.footer-section p {
  color: var(--gray-300);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-4);
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: var(--space-3);
}

.footer-section ul li a {
  color: var(--gray-300);
  text-decoration: none;
  transition: var(--transition-default);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) 0;
  border-radius: var(--radius-md);
}

.footer-section ul li a:hover,
.footer-section ul li a:focus {
  color: var(--primary-400);
  text-decoration: none;
  transform: translateX(4px);
}

.footer-section ul li a::before {
  content: '→';
  opacity: 0;
  transform: translateX(-8px);
  transition: var(--transition-default);
}

.footer-section ul li a:hover::before,
.footer-section ul li a:focus::before {
  opacity: 1;
  transform: translateX(0);
}

/* ===== CONTACT INFO STYLING ===== */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.contact-info p {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  color: var(--gray-300);
  margin-bottom: 0;
  padding: var(--space-3);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition-default);
}

.contact-info p:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.contact-info i {
  color: var(--primary-400);
  font-size: var(--text-lg);
  width: 20px;
  text-align: center;
}

/* ===== SOCIAL LINKS ===== */
.social-links {
  display: flex;
  gap: var(--space-4);
  margin-top: var(--space-4);
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  color: var(--gray-300);
  font-size: var(--text-xl);
  transition: var(--transition-default);
  position: relative;
  overflow: hidden;
}

.social-links a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  opacity: 0;
  transition: var(--transition-default);
  z-index: -1;
}

.social-links a:hover,
.social-links a:focus {
  color: white;
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  text-decoration: none;
}

.social-links a:hover::before,
.social-links a:focus::before {
  opacity: 1;
}

/* Platform-specific colors */
.social-links a[aria-label*="Facebook"]:hover {
  background: #1877f2;
  border-color: #1877f2;
}

.social-links a[aria-label*="Instagram"]:hover {
  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
  border-color: #e6683c;
}

.social-links a[aria-label*="YouTube"]:hover {
  background: #ff0000;
  border-color: #ff0000;
}

.social-links a[aria-label*="LinkedIn"]:hover {
  background: #0077b5;
  border-color: #0077b5;
}

/* ===== FOOTER BOTTOM ===== */
.footer-bottom {
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--space-6) var(--space-6);
  text-align: center;
  position: relative;
  z-index: 2;
}

.footer-bottom p {
  color: var(--gray-400);
  margin-bottom: 0;
  font-size: var(--text-sm);
}

.footer-bottom a {
  color: var(--primary-400);
  text-decoration: none;
  transition: var(--transition-default);
}

.footer-bottom a:hover,
.footer-bottom a:focus {
  color: var(--primary-300);
  text-decoration: underline;
}

/* ===== NEWSLETTER SIGNUP (Optional Enhancement) ===== */
.newsletter-signup {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-top: var(--space-6);
}

.newsletter-signup h4 {
  color: white;
  margin-bottom: var(--space-4);
  font-size: var(--text-lg);
}

.newsletter-form {
  display: flex;
  gap: var(--space-2);
}

.newsletter-form input {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: var(--text-sm);
  transition: var(--transition-default);
}

.newsletter-form input::placeholder {
  color: var(--gray-400);
}

.newsletter-form input:focus {
  outline: none;
  border-color: var(--primary-400);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.newsletter-form button {
  padding: var(--space-3) var(--space-6);
  background: var(--primary-600);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-weight: var(--font-semibold);
  cursor: pointer;
  transition: var(--transition-default);
  white-space: nowrap;
}

.newsletter-form button:hover,
.newsletter-form button:focus {
  background: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
  position: fixed;
  bottom: var(--space-8);
  right: var(--space-8);
  width: 56px;
  height: 56px;
  background: var(--primary-600);
  color: white;
  border: none;
  border-radius: var(--radius-full);
  font-size: var(--text-xl);
  cursor: pointer;
  transition: var(--transition-default);
  z-index: var(--z-fixed);
  box-shadow: var(--shadow-lg);
  opacity: 0;
  transform: translateY(20px);
  pointer-events: none;
}

.back-to-top.visible {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.back-to-top:hover,
.back-to-top:focus {
  background: var(--primary-700);
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-8);
  }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--space-8);
    padding: var(--space-16) var(--space-4) var(--space-8);
  }
  
  .social-links {
    justify-content: center;
  }
  
  .newsletter-form {
    flex-direction: column;
  }
  
  .newsletter-form button {
    align-self: stretch;
  }
  
  .back-to-top {
    bottom: var(--space-6);
    right: var(--space-6);
    width: 48px;
    height: 48px;
    font-size: var(--text-lg);
  }
}

@media (max-width: 640px) {
  .footer-content {
    padding: var(--space-12) var(--space-2) var(--space-6);
  }
  
  .footer-bottom {
    padding: var(--space-4) var(--space-2);
  }
  
  .footer-bottom p {
    font-size: var(--text-xs);
    line-height: var(--leading-relaxed);
  }
  
  .contact-info p {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
  }
  
  .social-links a {
    width: 44px;
    height: 44px;
    font-size: var(--text-lg);
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  .footer-section ul li a,
  .social-links a,
  .back-to-top {
    transition: none;
  }
  
  .footer-section ul li a:hover,
  .footer-section ul li a:focus {
    transform: none;
  }
  
  .social-links a:hover,
  .social-links a:focus,
  .back-to-top:hover,
  .back-to-top:focus {
    transform: none;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  footer {
    background: #000000;
    border-top: 2px solid #ffffff;
  }
  
  .footer-section h3,
  .footer-section h4 {
    color: #ffffff;
  }
  
  .footer-section p,
  .footer-section ul li a {
    color: #ffffff;
  }
  
  .social-links a {
    background: #ffffff;
    color: #000000;
    border: 2px solid #ffffff;
  }
}
