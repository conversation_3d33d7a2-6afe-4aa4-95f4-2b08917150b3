/* ===== GLASSMORPHISM EFFECTS TESTING SUITE ===== */
/* Comprehensive testing for all glassmorphism implementations */

class GlassmorphismTester {
  constructor() {
    this.testResults = [];
    this.supportedFeatures = {};
    this.init();
  }

  init() {
    console.log('🔍 Starting glassmorphism testing suite...');
    this.checkBrowserSupport();
    this.testAllGlassEffects();
    this.generateReport();
  }

  checkBrowserSupport() {
    console.log('🌐 Checking browser support for glassmorphism...');
    
    // Test backdrop-filter support
    const testElement = document.createElement('div');
    testElement.style.backdropFilter = 'blur(10px)';
    this.supportedFeatures.backdropFilter = testElement.style.backdropFilter !== '';
    
    // Test webkit-backdrop-filter support
    testElement.style.webkitBackdropFilter = 'blur(10px)';
    this.supportedFeatures.webkitBackdropFilter = testElement.style.webkitBackdropFilter !== '';
    
    // Overall support
    this.supportedFeatures.glassmorphismSupported = 
      this.supportedFeatures.backdropFilter || this.supportedFeatures.webkitBackdropFilter;
    
    console.log('Browser Support:', this.supportedFeatures);
  }

  testAllGlassEffects() {
    console.log('🔬 Testing all glassmorphism implementations...');
    
    // Test sections with glassmorphism
    const glassEffectSections = [
      // Career Pathways Section
      { selector: '.recognition-overview.glass-effect-strong', name: 'Recognition Overview', section: 'Career Pathways' },
      { selector: '.country-card.glass-effect', name: 'Country Cards', section: 'Career Pathways', multiple: true },
      { selector: '.alumni-success.glass-effect', name: 'Alumni Success', section: 'Career Pathways' },
      
      // Campus Life Section
      { selector: '.campus-overview.glass-effect-strong', name: 'Campus Overview', section: 'Campus Life' },
      { selector: '.campus-gallery.glass-effect', name: 'Campus Gallery', section: 'Campus Life' },
      { selector: '.facility-card.glass-effect', name: 'Facility Cards', section: 'Campus Life', multiple: true },
      { selector: '.virtual-tour.glass-effect', name: 'Virtual Tour', section: 'Campus Life' },
      
      // Testimonials Section
      { selector: '.testimonial-card.glass-effect', name: 'Testimonial Cards', section: 'Testimonials', multiple: true },
      
      // Safety & Support Section
      { selector: '.safety-card.glass-effect', name: 'Safety Cards', section: 'Safety & Support', multiple: true },
      
      // About Section
      { selector: '.about-card.glass-effect', name: 'About Cards', section: 'About FEFU', multiple: true },
      
      // Curriculum Section
      { selector: '.curriculum-overview.glass-effect-strong', name: 'Curriculum Overview', section: 'Curriculum' },
      { selector: '.year-card.glass-effect', name: 'Year Cards', section: 'Curriculum', multiple: true },
      { selector: '.assessment-system.glass-effect-strong', name: 'Assessment System', section: 'Curriculum' },
      
      // Clinical Training Section
      { selector: '.training-overview.glass-effect-strong', name: 'Training Overview', section: 'Clinical Training' },
      { selector: '.hospital-card.glass-effect', name: 'Hospital Cards', section: 'Clinical Training', multiple: true },
      { selector: '.rotation-schedule.glass-effect', name: 'Rotation Schedule', section: 'Clinical Training' },
      
      // Application Section
      { selector: '.cost-calculator-section.glass-effect', name: 'Cost Calculator', section: 'Application' },
      { selector: '.application-form', name: 'Application Form', section: 'Application' }
    ];

    glassEffectSections.forEach(test => this.testGlassEffect(test));
  }

  testGlassEffect(test) {
    const elements = document.querySelectorAll(test.selector);
    const elementCount = elements.length;
    
    if (elementCount === 0) {
      this.addTestResult(test.name, false, `No elements found with selector: ${test.selector}`, test.section);
      return;
    }

    if (test.multiple && elementCount === 1) {
      this.addTestResult(test.name, false, `Expected multiple elements, found only 1`, test.section);
      return;
    }

    let passedElements = 0;
    elements.forEach((element, index) => {
      const styles = getComputedStyle(element);
      const testResult = this.analyzeGlassEffect(element, styles, test.name, index);
      
      if (testResult.passed) {
        passedElements++;
      }
    });

    const allPassed = passedElements === elementCount;
    const description = test.multiple 
      ? `${passedElements}/${elementCount} elements have proper glassmorphism`
      : 'Element has proper glassmorphism';
    
    this.addTestResult(test.name, allPassed, description, test.section);
  }

  analyzeGlassEffect(element, styles, testName, index = 0) {
    const analysis = {
      passed: true,
      issues: []
    };

    // Check background
    const background = styles.background || styles.backgroundColor;
    if (!background.includes('rgba') && !background.includes('hsla')) {
      analysis.issues.push('No transparent background');
      analysis.passed = false;
    }

    // Check backdrop-filter
    const backdropFilter = styles.backdropFilter || styles.webkitBackdropFilter;
    if (!backdropFilter || backdropFilter === 'none') {
      if (this.supportedFeatures.glassmorphismSupported) {
        analysis.issues.push('No backdrop-filter applied');
        analysis.passed = false;
      } else {
        // Browser doesn't support backdrop-filter, check fallback
        if (!background.includes('rgba(255, 255, 255, 0.9')) {
          analysis.issues.push('No proper fallback background');
          analysis.passed = false;
        }
      }
    }

    // Check border
    const border = styles.border;
    if (!border || border === 'none' || !border.includes('rgba')) {
      analysis.issues.push('No glassmorphism border');
      analysis.passed = false;
    }

    // Check box-shadow
    const boxShadow = styles.boxShadow;
    if (!boxShadow || boxShadow === 'none') {
      analysis.issues.push('No shadow effect');
      analysis.passed = false;
    }

    // Log detailed analysis for failed elements
    if (!analysis.passed) {
      console.warn(`❌ ${testName} ${index > 0 ? `(${index + 1})` : ''} failed:`, analysis.issues);
      console.log('Element styles:', {
        background,
        backdropFilter,
        border,
        boxShadow
      });
    }

    return analysis;
  }

  addTestResult(testName, passed, description, section) {
    this.testResults.push({
      name: testName,
      section,
      passed,
      description,
      timestamp: new Date().toISOString()
    });
  }

  generateReport() {
    const passedTests = this.testResults.filter(test => test.passed).length;
    const totalTests = this.testResults.length;
    const passRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.group('🔍 Glassmorphism Testing Report');
    console.log(`Overall: ${passedTests}/${totalTests} glassmorphism implementations working (${passRate}%)`);
    console.log('Browser Support:', this.supportedFeatures);
    
    // Group results by section
    const sections = [...new Set(this.testResults.map(test => test.section))];
    
    sections.forEach(section => {
      const sectionTests = this.testResults.filter(test => test.section === section);
      const sectionPassed = sectionTests.filter(test => test.passed).length;
      
      console.group(`📋 ${section} Section (${sectionPassed}/${sectionTests.length})`);
      sectionTests.forEach(test => {
        const icon = test.passed ? '✅' : '❌';
        console.log(`${icon} ${test.name}: ${test.description}`);
      });
      console.groupEnd();
    });
    
    console.groupEnd();
    
    // Store results globally
    window.glassmorphismTestResults = {
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: totalTests - passedTests,
        passRate: passRate + '%',
        browserSupport: this.supportedFeatures
      },
      details: this.testResults,
      sections: sections.map(section => {
        const sectionTests = this.testResults.filter(test => test.section === section);
        return {
          name: section,
          total: sectionTests.length,
          passed: sectionTests.filter(test => test.passed).length,
          tests: sectionTests
        };
      })
    };
    
    // Show visual indicator
    this.showGlassmorphismIndicator(passRate);
    
    return this.testResults;
  }

  showGlassmorphismIndicator(passRate) {
    const indicator = document.createElement('div');
    indicator.id = 'glassmorphism-indicator';
    indicator.style.cssText = `
      position: fixed;
      top: 100px;
      right: 10px;
      z-index: 10000;
      padding: 12px 16px;
      border-radius: 12px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      ${passRate >= 95 ? 'background: rgba(40, 167, 69, 0.8);' : 
        passRate >= 80 ? 'background: rgba(255, 193, 7, 0.8); color: #000;' : 
        'background: rgba(220, 53, 69, 0.8);'}
    `;
    
    indicator.innerHTML = `
      <div>Glass: ${passRate}%</div>
      <div style="font-size: 10px; opacity: 0.8;">
        ${this.supportedFeatures.glassmorphismSupported ? 'Supported' : 'Fallback'}
      </div>
    `;
    
    indicator.addEventListener('click', () => {
      console.log('Glassmorphism Test Results:', window.glassmorphismTestResults);
    });
    
    document.body.appendChild(indicator);
    
    // Auto-hide after 15 seconds
    setTimeout(() => {
      if (indicator.parentNode) {
        indicator.style.opacity = '0';
        setTimeout(() => indicator.remove(), 300);
      }
    }, 15000);
  }
}

// Auto-run glassmorphism tests when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Wait for styles to load and components to initialize
  setTimeout(() => {
    new GlassmorphismTester();
  }, 3000);
});

// Manual test trigger
window.testGlassmorphism = function() {
  new GlassmorphismTester();
};

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GlassmorphismTester;
}
