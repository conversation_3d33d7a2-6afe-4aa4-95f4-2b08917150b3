// Premium Animation System
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { SplitText } from 'gsap/SplitText';

gsap.registerPlugin(ScrollTrigger, SplitText);

export class PremiumAnimations {
    constructor() {
        this.init();
    }

    init() {
        this.setupScrollAnimations();
        this.setupHeroAnimations();
        this.setupParallaxEffects();
        this.setupMagneticButtons();
        this.setupStaggerAnimations();
    }

    setupScrollAnimations() {
        // Reveal animations on scroll
        gsap.utils.toArray('.reveal-up').forEach(element => {
            gsap.from(element, {
                y: 50,
                opacity: 0,
                duration: 1,
                ease: 'power3.out',
                scrollTrigger: {
                    trigger: element,
                    start: 'top 80%',
                    toggleActions: 'play none none reverse'
                }
            });
        });

        // Scale animations
        gsap.utils.toArray('.reveal-scale').forEach(element => {
            gsap.from(element, {
                scale: 0.8,
                opacity: 0,
                duration: 1,
                ease: 'power3.out',
                scrollTrigger: {
                    trigger: element,
                    start: 'top 80%',
                    toggleActions: 'play none none reverse'
                }
            });
        });
    }

    setupHeroAnimations() {
        // Split text animation for hero title
        const heroTitle = new SplitText('.hero-title', { type: 'chars, words' });
        const heroBg = document.querySelector('.hero-background');

        const tl = gsap.timeline();

        tl.from(heroBg, {
            opacity: 0,
            scale: 1.1,
            duration: 1.5,
            ease: 'power3.out'
        })
        .from(heroTitle.chars, {
            opacity: 0,
            y: 50,
            rotateX: -90,
            stagger: 0.02,
            duration: 0.8,
            ease: 'power3.out'
        }, '-=1')
        .from('.hero-subtitle', {
            opacity: 0,
            y: 20,
            duration: 0.8,
            ease: 'power3.out'
        }, '-=0.4')
        .from('.hero-cta', {
            opacity: 0,
            y: 20,
            duration: 0.8,
            ease: 'power3.out'
        }, '-=0.4');
    }

    setupParallaxEffects() {
        // Smooth parallax for background elements
        gsap.utils.toArray('.parallax').forEach(element => {
            gsap.to(element, {
                y: '30%',
                ease: 'none',
                scrollTrigger: {
                    trigger: element,
                    start: 'top bottom',
                    end: 'bottom top',
                    scrub: true
                }
            });
        });
    }

    setupMagneticButtons() {
        // Magnetic effect for buttons
        gsap.utils.toArray('.magnetic-hover').forEach(button => {
            button.addEventListener('mousemove', (e) => {
                const rect = button.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                gsap.to(button, {
                    x: (x - rect.width / 2) / rect.width * 20,
                    y: (y - rect.height / 2) / rect.height * 20,
                    duration: 0.3,
                    ease: 'power3.out'
                });
            });

            button.addEventListener('mouseleave', () => {
                gsap.to(button, {
                    x: 0,
                    y: 0,
                    duration: 0.5,
                    ease: 'elastic.out(1, 0.3)'
                });
            });
        });
    }

    setupStaggerAnimations() {
        // Stagger animations for lists and grids
        gsap.utils.toArray('.stagger-container').forEach(container => {
            const items = container.querySelectorAll('.stagger-item');

            ScrollTrigger.create({
                trigger: container,
                start: 'top 80%',
                onEnter: () => {
                    gsap.to(items, {
                        opacity: 1,
                        y: 0,
                        duration: 0.8,
                        stagger: 0.1,
                        ease: 'power3.out'
                    });
                }
            });
        });
    }
}

// Initialize animations
document.addEventListener('DOMContentLoaded', () => {
    new PremiumAnimations();
});
