/* ===== MODERN MBBS VOSTRIX STYLES ===== */
/* Professional Medical Education Platform Design */

/* ===== RESET & BASE ===== */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: var(--font-sans);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--color-text-primary);
  background-color: var(--color-background);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* ===== FOCUS MANAGEMENT ===== */
:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-sans);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

h1 {
  font-size: var(--text-5xl);
  font-weight: var(--font-extrabold);
  line-height: var(--leading-none);
}

h2 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
}

h3 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
}

h4 {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
}

h5 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
}

h6 {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
}

p {
  margin-bottom: var(--space-4);
  color: var(--color-text-secondary);
  line-height: var(--leading-relaxed);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: var(--transition-default);
}

a:hover,
a:focus {
  color: var(--color-primary-hover);
  text-decoration: underline;
}

/* ===== MODERN HEADER ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid var(--color-border);
  transition: var(--transition-default);
}

.nav-container {
  max-width: var(--container-7xl);
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--header-height-lg);
}

.nav-brand h1 {
  font-size: var(--text-2xl);
  font-weight: var(--font-extrabold);
  color: var(--color-primary);
  margin-bottom: 0;
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tagline {
  font-size: var(--text-sm);
  color: var(--color-text-muted);
  font-weight: var(--font-medium);
  margin-top: var(--space-1);
}

.nav-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: var(--transition-default);
}

.nav-toggle span {
  display: block;
  width: 24px;
  height: 2px;
  background: var(--color-primary);
  margin: 4px 0;
  transition: var(--transition-default);
  border-radius: var(--radius-full);
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--space-8);
  align-items: center;
}

.nav-menu a {
  font-weight: var(--font-medium);
  color: var(--color-text-primary);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  transition: var(--transition-default);
  position: relative;
}

.nav-menu a:hover,
.nav-menu a:focus {
  color: var(--color-primary);
  background: var(--primary-50);
  text-decoration: none;
}

.cta-nav {
  background: var(--color-primary) !important;
  color: white !important;
  font-weight: var(--font-semibold);
  padding: var(--space-3) var(--space-6) !important;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-sm);
}

.cta-nav:hover,
.cta-nav:focus {
  background: var(--color-primary-hover) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* ===== QUICK INFO BAR ===== */
.quick-info-bar {
  position: fixed;
  top: var(--header-height-lg);
  left: 0;
  right: 0;
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
  color: white;
  z-index: var(--z-sticky);
  box-shadow: var(--shadow-md);
  animation: slideDown 0.5s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.quick-info-content {
  max-width: var(--container-7xl);
  margin: 0 auto;
  padding: var(--space-3) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-6);
}

.quick-info-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
}

.info-label {
  font-weight: var(--font-medium);
  opacity: 0.9;
}

.info-value {
  font-weight: var(--font-bold);
  background: rgba(255, 255, 255, 0.2);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
}

.quick-info-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-default);
}

.quick-info-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* ===== MODERN HERO SECTION ===== */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: calc(var(--header-height-lg) + 60px) var(--space-6) var(--space-20);
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="%23e5e7eb" stroke-width="1" opacity="0.3"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
  z-index: 1;
}

.hero-content {
  max-width: var(--container-7xl);
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero-text {
  animation: slideInLeft 0.8s ease-out;
}

.hero-title {
  font-size: var(--text-6xl);
  font-weight: var(--font-black);
  line-height: var(--leading-none);
  margin-bottom: var(--space-6);
  color: var(--color-text-primary);
}

.highlight {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.university-name {
  color: var(--color-secondary);
  font-weight: var(--font-extrabold);
}

.hero-subtitle {
  font-size: var(--text-xl);
  color: var(--color-text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-8);
  max-width: 600px;
}

.hero-cta {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-12);
  flex-wrap: wrap;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-full);
  font-weight: var(--font-semibold);
  font-size: var(--text-lg);
  transition: var(--transition-default);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  min-height: 48px; /* Touch target */
}

.cta-button.primary {
  background: var(--color-primary);
  color: white;
  box-shadow: var(--shadow-lg);
}

.cta-button.primary:hover,
.cta-button.primary:focus {
  background: var(--color-primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  text-decoration: none;
}

.cta-button.secondary {
  background: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
}

.cta-button.secondary:hover,
.cta-button.secondary:focus {
  background: var(--color-primary);
  color: white;
  text-decoration: none;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
  margin-top: var(--space-8);
}

.stat {
  text-align: center;
  padding: var(--space-6);
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-2xl);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition-default);
}

.stat:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stat-number {
  display: block;
  font-size: var(--text-3xl);
  font-weight: var(--font-extrabold);
  color: var(--color-primary);
  line-height: var(--leading-none);
  margin-bottom: var(--space-2);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-medium);
}

.urgency-indicators {
  margin-top: var(--space-8);
  padding: var(--space-6);
  background: rgba(220, 38, 38, 0.1);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.urgency-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
  font-size: var(--text-sm);
  color: var(--error-700);
}

.urgency-item:last-child {
  margin-bottom: 0;
}

.urgency-item i {
  color: var(--error-600);
  font-size: var(--text-base);
}

.urgency-item strong {
  color: var(--error-800);
}

.hero-visual {
  position: relative;
  animation: slideInRight 0.8s ease-out;
}

.globe-container {
  width: 100%;
  height: 500px;
  border-radius: var(--radius-3xl);
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
  background: var(--color-surface);
  position: relative;
}

/* ===== ANIMATIONS ===== */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-60px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(60px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    text-align: center;
  }
  
  .hero-title {
    font-size: var(--text-5xl);
  }
  
  .hero-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-4);
  }
}

/* ===== MOBILE STICKY CTA ===== */
.mobile-sticky-cta {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  z-index: var(--z-sticky);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(100%);
  transition: var(--transition-default);
  display: none;
}

.mobile-sticky-cta.visible {
  transform: translateY(0);
}

.sticky-cta-content {
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
}

.cta-text {
  display: flex;
  flex-direction: column;
}

.cta-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  line-height: 1.2;
}

.cta-subtitle {
  font-size: var(--text-sm);
  opacity: 0.9;
}

.sticky-apply-btn {
  background: var(--secondary-500);
  color: var(--primary-900);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-full);
  font-weight: var(--font-bold);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  transition: var(--transition-default);
  min-height: 44px;
  font-size: var(--text-sm);
}

.sticky-apply-btn:hover {
  background: var(--secondary-400);
  transform: scale(1.05);
  text-decoration: none;
  color: var(--primary-900);
}

@media (max-width: 768px) {
  .mobile-sticky-cta {
    display: block;
  }

  .quick-info-content {
    flex-direction: column;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
  }

  .quick-info-item {
    font-size: var(--text-xs);
  }

  .nav-toggle {
    display: block;
  }

  .nav-menu {
    display: none;
  }
  
  .hero-section {
    padding: var(--header-height) var(--space-4) var(--space-16);
  }
  
  .hero-title {
    font-size: var(--text-4xl);
  }
  
  .hero-subtitle {
    font-size: var(--text-lg);
  }
  
  .hero-cta {
    flex-direction: column;
    align-items: center;
  }
  
  .cta-button {
    width: 100%;
    justify-content: center;
    max-width: 300px;
  }
  
  .hero-stats {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .urgency-indicators {
    margin-top: var(--space-6);
    padding: var(--space-4);
  }

  .urgency-item {
    font-size: var(--text-xs);
    margin-bottom: var(--space-2);
  }
  
  .globe-container {
    height: 300px;
  }
}

@media (max-width: 640px) {
  .nav-container {
    padding: 0 var(--space-4);
  }
  
  .hero-title {
    font-size: var(--text-3xl);
  }
  
  .stat {
    padding: var(--space-4);
  }
  
  .stat-number {
    font-size: var(--text-2xl);
  }
}
