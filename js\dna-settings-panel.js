/* ===== DNA NAVIGATION SETTINGS PANEL ===== */
/* User preferences and customization interface */

class DNASettingsPanel {
  constructor(dnaNavigation) {
    this.dnaNavigation = dnaNavigation;
    this.panel = null;
    this.isVisible = false;
    this.init();
  }

  init() {
    this.createSettingsPanel();
    this.setupEventListeners();
    console.log('⚙️ DNA Settings Panel initialized');
  }

  createSettingsPanel() {
    this.panel = document.createElement('div');
    this.panel.id = 'dna-settings-panel';
    this.panel.className = 'dna-settings-panel';
    
    this.panel.innerHTML = `
      <div class="settings-header">
        <h3>🧬 DNA Navigation Settings</h3>
        <button class="close-btn" aria-label="Close settings">×</button>
      </div>
      
      <div class="settings-content">
        <div class="settings-section">
          <h4>🎵 Audio & Feedback</h4>
          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="audio-enabled" ${this.dnaNavigation.preferences.audioEnabled ? 'checked' : ''}>
              <span class="checkmark"></span>
              Enable audio feedback
            </label>
            <p class="setting-description">Play sounds for navigation and interactions</p>
          </div>
          
          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="haptic-enabled" ${this.dnaNavigation.preferences.hapticEnabled ? 'checked' : ''}>
              <span class="checkmark"></span>
              Enable haptic feedback
            </label>
            <p class="setting-description">Vibration feedback on mobile devices</p>
          </div>
        </div>

        <div class="settings-section">
          <h4>🎤 Voice Control</h4>
          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="voice-enabled" ${this.dnaNavigation.preferences.voiceEnabled ? 'checked' : ''}>
              <span class="checkmark"></span>
              Enable voice navigation
            </label>
            <p class="setting-description">Navigate using voice commands</p>
          </div>
          
          <div class="voice-commands">
            <h5>Voice Commands:</h5>
            <ul>
              <li>"Navigate to career" - Go to Career Pathways</li>
              <li>"Navigate to campus" - Go to Campus Life</li>
              <li>"Navigate to testimonials" - Go to Testimonials</li>
              <li>"Navigate to safety" - Go to Safety & Support</li>
              <li>"Navigate to about" - Go to About FEFU</li>
              <li>"Navigate to curriculum" - Go to Curriculum</li>
            </ul>
          </div>
        </div>

        <div class="settings-section">
          <h4>🎨 Visual Effects</h4>
          <div class="setting-item">
            <label class="setting-label">
              <input type="checkbox" id="particle-effects" ${this.dnaNavigation.preferences.particleEffects ? 'checked' : ''}>
              <span class="checkmark"></span>
              Enable particle effects
            </label>
            <p class="setting-description">Floating particles and visual enhancements</p>
          </div>
          
          <div class="setting-item">
            <label class="setting-label">
              Animation Speed
              <input type="range" id="animation-speed" min="0.5" max="2" step="0.1" value="${this.dnaNavigation.preferences.animationSpeed}">
              <span class="range-value">${this.dnaNavigation.preferences.animationSpeed}x</span>
            </label>
            <p class="setting-description">Adjust DNA helix animation speed</p>
          </div>
        </div>

        <div class="settings-section">
          <h4>⌨️ Keyboard Shortcuts</h4>
          <div class="shortcuts-list">
            <div class="shortcut-item">
              <kbd>1-6</kbd>
              <span>Navigate to sections 1-6</span>
            </div>
            <div class="shortcut-item">
              <kbd>Ctrl + V</kbd>
              <span>Toggle voice recognition</span>
            </div>
            <div class="shortcut-item">
              <kbd>Ctrl + A</kbd>
              <span>Toggle audio feedback</span>
            </div>
          </div>
        </div>

        <div class="settings-section">
          <h4>📊 Analytics & Data</h4>
          <div class="analytics-summary">
            <div class="analytics-item">
              <span class="analytics-label">Navigation clicks:</span>
              <span class="analytics-value">${this.dnaNavigation.analytics.navigationClicks || 0}</span>
            </div>
            <div class="analytics-item">
              <span class="analytics-label">Session time:</span>
              <span class="analytics-value">${this.formatSessionTime()}</span>
            </div>
            <div class="analytics-item">
              <span class="analytics-label">Sections completed:</span>
              <span class="analytics-value">${this.dnaNavigation.preferences.completedSections.length}/6</span>
            </div>
          </div>
          
          <div class="setting-item">
            <button class="action-btn" id="export-analytics">📥 Export Analytics</button>
            <button class="action-btn danger" id="reset-progress">🔄 Reset Progress</button>
          </div>
        </div>

        <div class="settings-section">
          <h4>🔧 Performance</h4>
          <div class="performance-info">
            <div class="performance-item">
              <span class="performance-label">Current FPS:</span>
              <span class="performance-value" id="current-fps">${this.dnaNavigation.performanceMonitor.fps}</span>
            </div>
            <div class="performance-item">
              <span class="performance-label">Quality Level:</span>
              <span class="performance-value" id="quality-level">${Math.round(this.dnaNavigation.performanceMonitor.adaptiveQuality * 100)}%</span>
            </div>
            <div class="performance-item">
              <span class="performance-label">WebGL Support:</span>
              <span class="performance-value">${this.dnaNavigation.isWebGLSupported ? '✅ Yes' : '❌ No'}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="settings-footer">
        <button class="action-btn primary" id="save-settings">💾 Save Settings</button>
        <button class="action-btn" id="reset-settings">↩️ Reset to Defaults</button>
      </div>
    `;
    
    this.panel.style.cssText = `
      position: fixed;
      top: 50%;
      right: -400px;
      width: 380px;
      max-height: 80vh;
      background: var(--glass-bg-strong);
      backdrop-filter: var(--glass-backdrop);
      -webkit-backdrop-filter: var(--glass-backdrop);
      border: 1px solid var(--glass-border);
      border-radius: 16px;
      box-shadow: var(--shadow-multi-layer);
      z-index: 10002;
      transform: translateY(-50%);
      transition: right 0.4s var(--ease-premium);
      overflow: hidden;
      font-family: var(--font-primary);
    `;
    
    document.body.appendChild(this.panel);
  }

  setupEventListeners() {
    // Close button
    this.panel.querySelector('.close-btn').addEventListener('click', () => {
      this.hide();
    });

    // Settings inputs
    this.panel.querySelector('#audio-enabled').addEventListener('change', (e) => {
      this.dnaNavigation.audioEnabled = e.target.checked;
      this.dnaNavigation.preferences.audioEnabled = e.target.checked;
      if (e.target.checked) {
        this.dnaNavigation.initializeAudioSystem();
        this.dnaNavigation.playNavigationSound('click');
      }
    });

    this.panel.querySelector('#haptic-enabled').addEventListener('change', (e) => {
      this.dnaNavigation.preferences.hapticEnabled = e.target.checked;
    });

    this.panel.querySelector('#voice-enabled').addEventListener('change', (e) => {
      this.dnaNavigation.voiceEnabled = e.target.checked;
      this.dnaNavigation.preferences.voiceEnabled = e.target.checked;
      if (e.target.checked) {
        this.dnaNavigation.initializeVoiceRecognition();
      }
    });

    this.panel.querySelector('#particle-effects').addEventListener('change', (e) => {
      this.dnaNavigation.preferences.particleEffects = e.target.checked;
      this.updateParticleEffects(e.target.checked);
    });

    this.panel.querySelector('#animation-speed').addEventListener('input', (e) => {
      const speed = parseFloat(e.target.value);
      this.dnaNavigation.preferences.animationSpeed = speed;
      this.panel.querySelector('.range-value').textContent = `${speed}x`;
    });

    // Action buttons
    this.panel.querySelector('#save-settings').addEventListener('click', () => {
      this.saveSettings();
    });

    this.panel.querySelector('#reset-settings').addEventListener('click', () => {
      this.resetSettings();
    });

    this.panel.querySelector('#export-analytics').addEventListener('click', () => {
      this.exportAnalytics();
    });

    this.panel.querySelector('#reset-progress').addEventListener('click', () => {
      this.resetProgress();
    });

    // Update performance info periodically
    setInterval(() => {
      this.updatePerformanceInfo();
    }, 1000);
  }

  show() {
    this.isVisible = true;
    this.panel.style.right = '20px';
    this.updatePerformanceInfo();
  }

  hide() {
    this.isVisible = false;
    this.panel.style.right = '-400px';
  }

  toggle() {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  updateParticleEffects(enabled) {
    if (this.dnaNavigation.segments) {
      this.dnaNavigation.segments.forEach(segment => {
        if (segment.particles) {
          segment.particles.visible = enabled;
        }
      });
    }
    
    if (this.dnaNavigation.ambientParticles) {
      this.dnaNavigation.ambientParticles.visible = enabled;
    }
  }

  updatePerformanceInfo() {
    if (!this.isVisible) return;
    
    const fpsElement = this.panel.querySelector('#current-fps');
    const qualityElement = this.panel.querySelector('#quality-level');
    
    if (fpsElement) {
      fpsElement.textContent = this.dnaNavigation.performanceMonitor.fps;
    }
    
    if (qualityElement) {
      qualityElement.textContent = `${Math.round(this.dnaNavigation.performanceMonitor.adaptiveQuality * 100)}%`;
    }
  }

  formatSessionTime() {
    const sessionTime = Date.now() - this.dnaNavigation.analytics.sessionStartTime;
    const minutes = Math.floor(sessionTime / 60000);
    const seconds = Math.floor((sessionTime % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  }

  saveSettings() {
    this.dnaNavigation.savePreferences();
    this.showNotification('Settings saved successfully!', 'success');
  }

  resetSettings() {
    if (confirm('Reset all settings to defaults? This cannot be undone.')) {
      this.dnaNavigation.preferences = this.dnaNavigation.loadPreferences();
      localStorage.removeItem('dna-navigation-preferences');
      this.showNotification('Settings reset to defaults', 'info');
      this.updateSettingsUI();
    }
  }

  exportAnalytics() {
    const analyticsData = {
      preferences: this.dnaNavigation.preferences,
      analytics: this.dnaNavigation.analytics,
      performance: this.dnaNavigation.performanceMonitor,
      exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(analyticsData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dna-navigation-analytics-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    this.showNotification('Analytics exported successfully!', 'success');
  }

  resetProgress() {
    if (confirm('Reset all progress data? This will clear completed sections and engagement time.')) {
      this.dnaNavigation.preferences.completedSections = [];
      this.dnaNavigation.preferences.sectionProgress = {};
      this.dnaNavigation.analytics.sectionEngagementTime = {};
      this.dnaNavigation.savePreferences();
      this.showNotification('Progress data reset', 'info');
      this.updateSettingsUI();
    }
  }

  updateSettingsUI() {
    // Update all form inputs to reflect current preferences
    const prefs = this.dnaNavigation.preferences;
    
    this.panel.querySelector('#audio-enabled').checked = prefs.audioEnabled;
    this.panel.querySelector('#haptic-enabled').checked = prefs.hapticEnabled;
    this.panel.querySelector('#voice-enabled').checked = prefs.voiceEnabled;
    this.panel.querySelector('#particle-effects').checked = prefs.particleEffects;
    this.panel.querySelector('#animation-speed').value = prefs.animationSpeed;
    this.panel.querySelector('.range-value').textContent = `${prefs.animationSpeed}x`;
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `settings-notification ${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 16px;
      background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
      color: white;
      border-radius: 8px;
      z-index: 10003;
      font-size: 14px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
      animation: slideInRight 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.animation = 'slideOutRight 0.3s ease-in forwards';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }
}

// Auto-initialize settings panel when DNA navigation is ready
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    if (window.dnaNavigation) {
      window.dnaSettingsPanel = new DNASettingsPanel(window.dnaNavigation);
      
      // Add settings button to DNA navigation toggle
      const dnaToggle = document.getElementById('dna-nav-toggle');
      if (dnaToggle) {
        dnaToggle.addEventListener('contextmenu', (e) => {
          e.preventDefault();
          window.dnaSettingsPanel.toggle();
        });
      }
      
      // Add keyboard shortcut for settings
      document.addEventListener('keydown', (e) => {
        if (e.key.toLowerCase() === 's' && e.ctrlKey && e.shiftKey) {
          e.preventDefault();
          window.dnaSettingsPanel.toggle();
        }
      });
    }
  }, 3000);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DNASettingsPanel;
}
