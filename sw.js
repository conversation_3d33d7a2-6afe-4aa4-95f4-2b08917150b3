// MBBS Vostrix Service Worker
const CACHE_NAME = 'mbbs-vostrix-v1.2.0';
const STATIC_CACHE = 'mbbs-vostrix-static-v1.2.0';
const DYNAMIC_CACHE = 'mbbs-vostrix-dynamic-v1.2.0';
const IMAGE_CACHE = 'mbbs-vostrix-images-v1.2.0';

// Assets to cache immediately - only existing files
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/css/optimized-styles.css',
  '/css/responsive.css',
  '/css/components.css',
  '/css/advanced-components.css',
  '/js/optimized-main.js',
  '/js/optimized-globe.js',
  '/js/optimized-campus-gallery.js',
  '/js/optimized-apply-button.js',
  '/js/infoCards.js',
  '/js/advantages.js',
  '/js/campusGallery.js',
  '/js/applyButton.js',
  '/js/textureGenerator.js',
  '/js/advanced-form.js',
  '/js/advanced-image-loader.js',
  '/assets/earth_texture.jpg',
  '/assets/favicon.svg',
  '/assets/og-image.svg',
  '/assets/twitter-image.svg',
  '/assets/icons/icon-192.svg',
  '/assets/icons/icon-512.svg',
  '/manifest.json',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
  'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap',
  'https://unpkg.com/three@0.158.0/build/three.module.js',
  'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js'
];

// Network-first resources (always try network first)
const NETWORK_FIRST = [
  '/api/',
  '/analytics/',
  '/tracking/'
];

// Cache-first resources (serve from cache if available)
const CACHE_FIRST = [
  '/assets/',
  '/css/',
  '/js/',
  'https://fonts.googleapis.com/',
  'https://fonts.gstatic.com/',
  'https://cdnjs.cloudflare.com/',
  'https://unpkg.com/'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      }),
      
      // Skip waiting to activate immediately
      self.skipWaiting()
    ])
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && 
                cacheName !== IMAGE_CACHE) {
              console.log('Service Worker: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      
      // Take control of all clients
      self.clients.claim()
    ])
  );
});

// Fetch event - handle requests with different strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Handle different types of requests
  if (isNetworkFirst(request.url)) {
    event.respondWith(networkFirst(request));
  } else if (isCacheFirst(request.url)) {
    event.respondWith(cacheFirst(request));
  } else if (isImage(request.url)) {
    event.respondWith(imageStrategy(request));
  } else {
    event.respondWith(staleWhileRevalidate(request));
  }
});

// Network-first strategy
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache:', error);
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return main page for navigation requests when offline
    if (request.mode === 'navigate') {
      return caches.match('/index.html');
    }
    
    throw error;
  }
}

// Cache-first strategy
async function cacheFirst(request) {
  const cachedResponse = await caches.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Cache and network failed:', error);
    throw error;
  }
}

// Stale-while-revalidate strategy
async function staleWhileRevalidate(request) {
  const cachedResponse = await caches.match(request);
  
  const fetchPromise = fetch(request).then((networkResponse) => {
    if (networkResponse.ok) {
      const cache = caches.open(DYNAMIC_CACHE);
      cache.then(c => c.put(request, networkResponse.clone()));
    }
    return networkResponse;
  }).catch(() => {
    // Network failed, return cached version if available
    return cachedResponse;
  });
  
  return cachedResponse || fetchPromise;
}

// Image-specific strategy with compression
async function imageStrategy(request) {
  const cachedResponse = await caches.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Compress and cache images
      const compressedResponse = await compressImage(networkResponse.clone());
      const cache = await caches.open(IMAGE_CACHE);
      cache.put(request, compressedResponse.clone());
      return compressedResponse;
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Image loading failed:', error);
    // Return a simple fallback response for failed images
    return new Response('', { status: 404, statusText: 'Image not found' });
  }
}

// Image compression utility
async function compressImage(response) {
  try {
    const blob = await response.blob();
    
    // Only compress if it's an image and larger than 100KB
    if (!blob.type.startsWith('image/') || blob.size < 100000) {
      return response;
    }
    
    const canvas = new OffscreenCanvas(800, 600);
    const ctx = canvas.getContext('2d');
    
    const bitmap = await createImageBitmap(blob);
    
    // Calculate new dimensions maintaining aspect ratio
    const { width, height } = calculateDimensions(bitmap.width, bitmap.height, 800, 600);
    
    canvas.width = width;
    canvas.height = height;
    
    ctx.drawImage(bitmap, 0, 0, width, height);
    
    const compressedBlob = await canvas.convertToBlob({
      type: 'image/jpeg',
      quality: 0.8
    });
    
    return new Response(compressedBlob, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers
    });
  } catch (error) {
    console.log('Image compression failed:', error);
    return response;
  }
}

// Calculate dimensions maintaining aspect ratio
function calculateDimensions(originalWidth, originalHeight, maxWidth, maxHeight) {
  const aspectRatio = originalWidth / originalHeight;
  
  let width = originalWidth;
  let height = originalHeight;
  
  if (width > maxWidth) {
    width = maxWidth;
    height = width / aspectRatio;
  }
  
  if (height > maxHeight) {
    height = maxHeight;
    width = height * aspectRatio;
  }
  
  return { width: Math.round(width), height: Math.round(height) };
}

// Helper functions
function isNetworkFirst(url) {
  return NETWORK_FIRST.some(pattern => url.includes(pattern));
}

function isCacheFirst(url) {
  return CACHE_FIRST.some(pattern => url.includes(pattern));
}

function isImage(url) {
  return /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(url) || url.includes('images.unsplash.com');
}

// Background sync for offline form submissions
self.addEventListener('sync', (event) => {
  if (event.tag === 'form-submission') {
    event.waitUntil(syncFormSubmissions());
  }
});

async function syncFormSubmissions() {
  try {
    const db = await openDB();
    const submissions = await getOfflineSubmissions(db);
    
    for (const submission of submissions) {
      try {
        const response = await fetch('/api/submit-form', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(submission.data)
        });
        
        if (response.ok) {
          await deleteSubmission(db, submission.id);
          console.log('Offline form submission synced:', submission.id);
        }
      } catch (error) {
        console.log('Failed to sync submission:', submission.id, error);
      }
    }
  } catch (error) {
    console.log('Background sync failed:', error);
  }
}

// Push notification handling
self.addEventListener('push', (event) => {
  if (!event.data) return;
  
  const data = event.data.json();
  
  const options = {
    body: data.body,
    icon: '/assets/icon-192.png',
    badge: '/assets/badge-72.png',
    image: data.image,
    data: data.url,
    actions: [
      {
        action: 'open',
        title: 'Open',
        icon: '/assets/open-icon.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/assets/close-icon.png'
      }
    ],
    requireInteraction: true,
    tag: 'mbbs-vostrix-notification'
  };
  
  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  if (event.action === 'open' || !event.action) {
    const url = event.notification.data || '/';
    
    event.waitUntil(
      clients.matchAll({ type: 'window' }).then((clientList) => {
        // Check if window is already open
        for (const client of clientList) {
          if (client.url === url && 'focus' in client) {
            return client.focus();
          }
        }
        
        // Open new window
        if (clients.openWindow) {
          return clients.openWindow(url);
        }
      })
    );
  }
});

// IndexedDB utilities for offline storage
function openDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('mbbs-vostrix-offline', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      
      if (!db.objectStoreNames.contains('form-submissions')) {
        const store = db.createObjectStore('form-submissions', { keyPath: 'id', autoIncrement: true });
        store.createIndex('timestamp', 'timestamp', { unique: false });
      }
    };
  });
}

function getOfflineSubmissions(db) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['form-submissions'], 'readonly');
    const store = transaction.objectStore('form-submissions');
    const request = store.getAll();
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
  });
}

function deleteSubmission(db, id) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['form-submissions'], 'readwrite');
    const store = transaction.objectStore('form-submissions');
    const request = store.delete(id);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve();
  });
}

// Performance monitoring
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'PERFORMANCE_REPORT') {
    console.log('Performance report:', event.data.metrics);
    
    // Send to analytics if needed
    if (event.data.metrics.loadTime > 3000) {
      console.warn('Slow page load detected:', event.data.metrics.loadTime);
    }
  }
});

console.log('Service Worker: Loaded successfully');
