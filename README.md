# MBBS Vostrix - Interactive 3D Website

A modern, interactive 3D website for MBBS Vostrix, a consultancy promoting the English-medium MBBS program at Far Eastern Federal University (FEFU) in Vladivostok, Russia.

## 🌟 Features

### Interactive 3D Elements
- **3D Globe**: Rotatable Earth with clickable Vladivostok hotspot
- **3D Info Cards**: Flip cards showing program details with hover effects
- **3D Campus Gallery**: Auto-rotating carousel of campus images
- **3D Advantages Section**: Interactive icons with animations
- **3D Apply Button**: Animated call-to-action with particle effects

### Key Information Displayed
- **Tuition**: ₹3.8-4.95 Lakhs/year (440,000-495,000 RUB)
- **Accommodation**: ₹0.4-0.5 Lakhs/year (44,000-50,000 RUB)
- **Living Costs**: ₹13,000-17,000/month (15,000-20,000 RUB)
- **Eligibility**: 50% in PCB + NEET qualification
- **Duration**: 6 years including internship
- **Recognition**: WHO & NMC approved

## 🚀 Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **3D Graphics**: Three.js
- **Animations**: GSAP, CSS animations
- **Responsive Design**: Mobile-first approach
- **Performance**: Lazy loading, WebGL fallbacks

## 📁 Project Structure

```
website 1/
├── index.html              # Main HTML file
├── css/
│   ├── styles.css          # Main styles
│   ├── animations.css      # Animation definitions
│   └── additional-styles.css # Extended styles
├── js/
│   ├── main.js            # Main application orchestrator
│   ├── globe.js           # 3D globe component
│   ├── infoCards.js       # 3D flip cards
│   ├── campusGallery.js   # 3D campus gallery
│   ├── advantages.js      # Advantages section
│   ├── applyButton.js     # 3D apply button
│   └── textureGenerator.js # Texture generation utilities
├── assets/
│   └── earth_texture.jpg  # Earth texture for globe
└── README.md              # This file
```

## 🎨 Design Theme

- **Colors**: White background, FEFU blue (#0056b3), gold accents (#FFD700)
- **Typography**: Roboto and Open Sans fonts
- **Style**: Minimalist, professional, trustworthy
- **Language**: English throughout

## 📱 Responsive Design

### Desktop (1200px+)
- Full 3D experience with all interactive elements
- Smooth animations and transitions
- Optimized for mouse interactions

### Tablet (768px - 1199px)
- Simplified 3D elements
- Touch-friendly interface
- Maintained visual hierarchy

### Mobile (< 768px)
- Fallback to 2D alternatives where needed
- Optimized touch interactions
- Stacked layout for better readability

## 🔧 Setup & Installation

1. **Clone or download the project**
2. **Start a local server**:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve . -p 8000
   
   # Using PHP
   php -S localhost:8000
   ```
3. **Open browser** and navigate to `http://localhost:8000`

## 🌐 Browser Support

- **Chrome**: 90+ (Recommended)
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

### WebGL Requirements
- Modern browsers with WebGL support
- Automatic fallback to 2D alternatives for unsupported devices

## 📊 Performance Optimizations

- **Lazy Loading**: Images and 3D components load on demand
- **Texture Optimization**: Compressed textures for faster loading
- **Fallback Systems**: 2D alternatives for low-end devices
- **Efficient Rendering**: Optimized Three.js scenes

## 🎯 Target Audience

- International medical students (primarily from India)
- Age group: 17-25 years
- Seeking affordable, quality medical education abroad
- English-speaking students

## 📋 Content Guidelines

### Verified Information Sources
- Official FEFU website and documentation
- WHO World Directory of Medical Schools
- NMC (National Medical Commission) guidelines
- Current exchange rates and fee structures

### Key Messages
- English-medium education throughout 6 years
- WHO/NMC recognition for global practice
- Affordable fees compared to alternatives
- Modern campus and facilities
- Strong international partnerships

## 🔄 Future Enhancements

### Planned Features
- Virtual campus tour integration
- Student testimonial videos
- Live chat support
- Application tracking system
- Multi-language support

### Technical Improvements
- Progressive Web App (PWA) capabilities
- Advanced 3D models and textures
- VR/AR compatibility
- Enhanced mobile experience

## 🛠️ Development Notes

### Component Architecture
Each 3D component is modular and can be:
- Independently loaded/unloaded
- Easily customized or replaced
- Gracefully degraded for unsupported devices

### Animation System
- GSAP for complex animations
- CSS animations for simple effects
- Intersection Observer for scroll-triggered animations
- Reduced motion support for accessibility

### Error Handling
- Graceful fallbacks for WebGL failures
- Progressive enhancement approach
- User-friendly error messages

## 📞 Contact Information

**MBBS Vostrix**
- Email: <EMAIL>
- Phone: +91 XXXXXXXXXX
- Website: [mbbsvostrix.com](https://mbbsvostrix.com)

## 📄 License

This project is created for MBBS Vostrix consultancy. All rights reserved.

---

**Note**: This website showcases the MBBS program at Far Eastern Federal University (FEFU) in Vladivostok, Russia. All information is based on official sources and current as of 2024. For the most up-to-date information, please contact MBBS Vostrix directly.
