/* ===== DNA NAVIGATION SYSTEM ===== */
/* Sophisticated scroll-responsive DNA helix navigation component */

// Prevent class redeclaration
if (typeof window.DNANavigation !== 'undefined') {
  console.warn('DNANavigation already exists, skipping redeclaration');
} else {

class DNANavigation {
  constructor() {
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.dnaHelix = null;
    this.segments = [];
    this.animationId = null;
    this.isWebGLSupported = this.checkWebGLSupport();
    this.isMobile = window.innerWidth < 768;
    this.isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;
    this.currentSection = 0;
    this.scrollProgress = 0;

    // Performance monitoring
    this.performanceMonitor = {
      fps: 60,
      frameCount: 0,
      lastTime: performance.now(),
      adaptiveQuality: 1.0,
      isLowPerformance: false
    };

    // Memory management
    this.objectPool = {
      particles: [],
      geometries: new Map(),
      materials: new Map()
    };

    // User preferences
    this.preferences = this.loadPreferences();

    // Audio system
    this.audioContext = null;
    this.audioEnabled = this.preferences.audioEnabled;

    // Voice recognition
    this.speechRecognition = null;
    this.voiceEnabled = this.preferences.voiceEnabled;

    // Analytics
    this.analytics = {
      navigationClicks: 0,
      sectionEngagementTime: {},
      sessionStartTime: Date.now()
    };
    
    // Section configuration
    this.sections = [
      { id: 'career-paths', name: 'Career Pathways', color: '#2563eb', completed: false },
      { id: 'campus', name: 'Campus Life', color: '#0891b2', completed: false },
      { id: 'testimonials', name: 'Testimonials', color: '#059669', completed: false },
      { id: 'safety-support', name: 'Safety & Support', color: '#dc2626', completed: false },
      { id: 'about', name: 'About FEFU', color: '#7c3aed', completed: false },
      { id: 'curriculum', name: 'Curriculum', color: '#ea580c', completed: false }
    ];

    this.init();
  }

  checkWebGLSupport() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      return !!gl;
    } catch (e) {
      return false;
    }
  }

  startPerformanceMonitoring() {
    console.log('📊 Starting performance monitoring...');

    setInterval(() => {
      this.updatePerformanceMetrics();
      this.adjustQualityBasedOnPerformance();
    }, 1000);
  }

  updatePerformanceMetrics() {
    const currentTime = performance.now();
    const deltaTime = currentTime - this.performanceMonitor.lastTime;

    if (deltaTime >= 1000) {
      this.performanceMonitor.fps = Math.round((this.performanceMonitor.frameCount * 1000) / deltaTime);
      this.performanceMonitor.frameCount = 0;
      this.performanceMonitor.lastTime = currentTime;

      // Update performance indicator
      this.updatePerformanceIndicator();
    }
  }

  adjustQualityBasedOnPerformance() {
    const targetFPS = 60;
    const currentFPS = this.performanceMonitor.fps;

    if (currentFPS < 45 && !this.performanceMonitor.isLowPerformance) {
      console.log('⚡ Switching to low performance mode');
      this.performanceMonitor.isLowPerformance = true;
      this.performanceMonitor.adaptiveQuality = 0.5;
      this.updateDNAQuality();
    } else if (currentFPS > 55 && this.performanceMonitor.isLowPerformance) {
      console.log('🚀 Switching to high performance mode');
      this.performanceMonitor.isLowPerformance = false;
      this.performanceMonitor.adaptiveQuality = 1.0;
      this.updateDNAQuality();
    }
  }

  updateDNAQuality() {
    if (!this.segments) return;

    const quality = this.performanceMonitor.adaptiveQuality;

    this.segments.forEach(segment => {
      if (segment.particles) {
        // Adjust particle count based on performance
        const targetParticleCount = Math.floor(20 * quality);
        this.adjustParticleCount(segment.particles, targetParticleCount);
      }

      // Adjust material quality
      if (segment.strand1Material && segment.strand2Material) {
        segment.strand1Material.shininess = 100 * quality;
        segment.strand2Material.shininess = 100 * quality;
      }
    });
  }

  loadPreferences() {
    const defaultPreferences = {
      audioEnabled: false,
      voiceEnabled: false,
      hapticEnabled: true,
      animationSpeed: 1.0,
      particleEffects: true,
      completedSections: [],
      sectionProgress: {}
    };

    try {
      const saved = localStorage.getItem('dna-navigation-preferences');
      return saved ? { ...defaultPreferences, ...JSON.parse(saved) } : defaultPreferences;
    } catch (error) {
      console.warn('Failed to load preferences:', error);
      return defaultPreferences;
    }
  }

  savePreferences() {
    try {
      localStorage.setItem('dna-navigation-preferences', JSON.stringify(this.preferences));
    } catch (error) {
      console.warn('Failed to save preferences:', error);
    }
  }

  initializeAudioSystem() {
    if (!this.audioEnabled) return;

    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      console.log('🔊 Audio system initialized');
    } catch (error) {
      console.warn('Audio not supported:', error);
      this.audioEnabled = false;
    }
  }

  playNavigationSound(type = 'click') {
    if (!this.audioEnabled || !this.audioContext) return;

    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    // Different sounds for different actions
    switch (type) {
      case 'click':
        oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.1);
        break;
      case 'complete':
        oscillator.frequency.setValueAtTime(600, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(900, this.audioContext.currentTime + 0.2);
        break;
      case 'hover':
        oscillator.frequency.setValueAtTime(1000, this.audioContext.currentTime);
        break;
    }

    gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.2);

    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + 0.2);
  }

  initializeVoiceRecognition() {
    if (!this.voiceEnabled || !('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    this.speechRecognition = new SpeechRecognition();

    this.speechRecognition.continuous = false;
    this.speechRecognition.interimResults = false;
    this.speechRecognition.lang = 'en-US';

    this.speechRecognition.onresult = (event) => {
      const command = event.results[0][0].transcript.toLowerCase();
      this.processVoiceCommand(command);
    };

    this.speechRecognition.onerror = (event) => {
      console.warn('Voice recognition error:', event.error);
    };

    console.log('🎤 Voice recognition initialized');
  }

  processVoiceCommand(command) {
    console.log('🎤 Voice command:', command);

    const sectionMap = {
      'career': 0, 'pathways': 0,
      'campus': 1, 'life': 1,
      'testimonials': 2, 'reviews': 2,
      'safety': 3, 'support': 3,
      'about': 4, 'fefu': 4,
      'curriculum': 5, 'courses': 5
    };

    for (const [keyword, index] of Object.entries(sectionMap)) {
      if (command.includes(keyword)) {
        this.navigateToSection(index);
        this.playNavigationSound('click');
        break;
      }
    }
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (event) => {
      // Only handle shortcuts when not in input fields
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') return;

      const key = event.key;

      // Number keys 1-6 for section navigation
      if (key >= '1' && key <= '6') {
        event.preventDefault();
        const sectionIndex = parseInt(key) - 1;
        this.navigateToSection(sectionIndex);
        this.playNavigationSound('click');
        this.trackAnalytics('keyboard_navigation', { section: sectionIndex });
      }

      // V key to toggle voice recognition
      if (key.toLowerCase() === 'v' && event.ctrlKey) {
        event.preventDefault();
        this.toggleVoiceRecognition();
      }

      // A key to toggle audio
      if (key.toLowerCase() === 'a' && event.ctrlKey) {
        event.preventDefault();
        this.toggleAudio();
      }
    });
  }

  setupGestureSupport() {
    if (!this.isMobile || !this.container) return;

    let startY = 0;
    let startTime = 0;

    this.container.addEventListener('touchstart', (event) => {
      if (event.touches && event.touches[0]) {
        startY = event.touches[0].clientY;
        startTime = Date.now();
      }
    }, { passive: true });

    this.container.addEventListener('touchend', (event) => {
      if (event.changedTouches && event.changedTouches[0]) {
        const endY = event.changedTouches[0].clientY;
        const endTime = Date.now();
        const deltaY = startY - endY;
        const deltaTime = endTime - startTime;

        // Swipe detection
        if (Math.abs(deltaY) > 50 && deltaTime < 300) {
          const direction = deltaY > 0 ? 'up' : 'down';
          this.handleSwipeGesture(direction);
        }
      }
    }, { passive: true });
  }

  handleSwipeGesture(direction) {
    const currentIndex = this.currentSection;
    let targetIndex;

    if (direction === 'up' && currentIndex < this.sections.length - 1) {
      targetIndex = currentIndex + 1;
    } else if (direction === 'down' && currentIndex > 0) {
      targetIndex = currentIndex - 1;
    }

    if (targetIndex !== undefined) {
      this.navigateToSection(targetIndex);
      this.triggerHapticFeedback();
      this.playNavigationSound('click');
    }
  }

  triggerHapticFeedback(type = 'light') {
    if (!this.preferences.hapticEnabled || !navigator.vibrate) return;

    const patterns = {
      light: [10],
      medium: [20],
      heavy: [30],
      success: [10, 50, 10]
    };

    navigator.vibrate(patterns[type] || patterns.light);
  }

  // ===== UTILITY METHODS =====

  async waitFor(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async createEnhancedCSSFallback() {
    console.log('🧬 Creating enhanced CSS fallback for DNA navigation...');

    this.container.innerHTML = `
      <div class="dna-fallback enhanced">
        <div class="dna-fallback-content">
          <div class="fallback-helix">
            ${Array.from({ length: 6 }, (_, i) => `
              <div class="fallback-segment" data-section="${this.sections[i].id}" style="animation-delay: ${i * 0.1}s">
                <div class="segment-indicator" style="background-color: ${this.sections[i].color}"></div>
                <div class="segment-info">
                  <div class="segment-name">${this.sections[i].name}</div>
                  <div class="segment-progress" style="width: ${this.preferences.sectionProgress[this.sections[i].id] * 100 || 0}%; background-color: ${this.sections[i].color}"></div>
                </div>
              </div>
            `).join('')}
          </div>
          <div class="fallback-info">
            <h4>DNA Navigation</h4>
            <p>CSS fallback mode - WebGL not available</p>
          </div>
        </div>
      </div>
    `;

    // Add enhanced CSS fallback styles
    this.setupEnhancedCSSFallbackStyles();
    this.setupCSSFallbackInteractions();

    // Initialize fallback navigation immediately
    this.initializeFallbackNavigation();
  }

  setupEnhancedCSSFallbackStyles() {
    if (document.getElementById('dna-enhanced-fallback-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'dna-enhanced-fallback-styles';
    styles.textContent = `
      .dna-fallback.enhanced {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 15px;
        background: var(--glass-bg-strong);
        backdrop-filter: var(--glass-backdrop);
        -webkit-backdrop-filter: var(--glass-backdrop);
        border-radius: inherit;
      }

      .dna-fallback-content {
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .fallback-helix {
        display: flex;
        flex-direction: column;
        gap: 8px;
        flex: 1;
      }

      .fallback-segment {
        position: relative;
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        animation: fadeInUp 0.5s ease forwards;
        opacity: 0;
        transform: translateY(20px);
      }

      .fallback-segment:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        transform: translateY(0) scale(1.02);
      }

      .segment-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        flex-shrink: 0;
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
      }

      .segment-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .segment-name {
        font-size: 0.85rem;
        font-weight: var(--font-medium);
        color: var(--color-text);
      }

      .segment-progress {
        height: 2px;
        border-radius: 1px;
        transition: width 0.3s ease;
        opacity: 0.7;
      }

      .fallback-info {
        text-align: center;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        color: var(--color-text);
      }

      .fallback-info h4 {
        margin: 0 0 4px 0;
        font-size: 0.9rem;
        color: var(--primary-600);
      }

      .fallback-info p {
        margin: 0;
        font-size: 0.75rem;
        opacity: 0.7;
      }

      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    `;
    document.head.appendChild(styles);
  }

  initializeFallbackNavigation() {
    const fallbackSegments = this.container.querySelectorAll('.fallback-segment');

    fallbackSegments.forEach((segment, index) => {
      const section = this.sections[index];

      // Add click handler for navigation
      segment.addEventListener('click', () => {
        this.navigateToSection(index);
      });

      // Add hover tooltip
      segment.addEventListener('mouseenter', () => {
        const rect = segment.getBoundingClientRect();
        this.showTooltip(index, rect.right + 10, rect.top + rect.height / 2);
      });

      // Remove tooltip on mouse leave
      segment.addEventListener('mouseleave', () => {
        const tooltip = document.getElementById('dna-tooltip');
        if (tooltip) tooltip.remove();
      });
    });

    console.log('✅ DNA Navigation CSS fallback initialized');
  }

  // showLoadingState method removed - direct initialization without skeleton

  // hideLoadingState method removed - no longer needed without skeleton

  trackAnalytics(action, data = {}) {
    this.analytics[action] = (this.analytics[action] || 0) + 1;

    const analyticsData = {
      action,
      timestamp: Date.now(),
      sessionTime: Date.now() - this.analytics.sessionStartTime,
      currentSection: this.currentSection,
      ...data
    };

    // Store in localStorage for persistence
    try {
      const existingData = JSON.parse(localStorage.getItem('dna-navigation-analytics') || '[]');
      existingData.push(analyticsData);

      // Keep only last 100 events
      if (existingData.length > 100) {
        existingData.splice(0, existingData.length - 100);
      }

      localStorage.setItem('dna-navigation-analytics', JSON.stringify(existingData));
    } catch (error) {
      console.warn('Failed to store analytics:', error);
    }

    console.log('📊 Analytics:', analyticsData);
  }

  updatePerformanceIndicator() {
    let indicator = document.getElementById('dna-performance-indicator');

    if (!indicator) {
      indicator = document.createElement('div');
      indicator.id = 'dna-performance-indicator';
      indicator.style.cssText = `
        position: fixed;
        top: 250px;
        right: 10px;
        z-index: 10000;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 11px;
        font-weight: bold;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
      `;
      document.body.appendChild(indicator);
    }

    const fps = this.performanceMonitor.fps;
    const quality = this.performanceMonitor.adaptiveQuality;
    const color = fps >= 55 ? '#28a745' : fps >= 45 ? '#ffc107' : '#dc3545';

    indicator.style.borderColor = color;
    indicator.innerHTML = `
      <div>🧬 ${fps} FPS</div>
      <div style="font-size: 9px; opacity: 0.8;">Quality: ${Math.round(quality * 100)}%</div>
    `;

    // Auto-hide after 5 seconds of good performance
    if (fps >= 55) {
      setTimeout(() => {
        if (indicator.parentNode && this.performanceMonitor.fps >= 55) {
          indicator.style.opacity = '0.3';
        }
      }, 5000);
    } else {
      indicator.style.opacity = '1';
    }
  }

  toggleVoiceRecognition() {
    this.voiceEnabled = !this.voiceEnabled;
    this.preferences.voiceEnabled = this.voiceEnabled;
    this.savePreferences();

    if (this.voiceEnabled && this.speechRecognition) {
      this.speechRecognition.start();
      console.log('🎤 Voice recognition activated');
    } else {
      console.log('🎤 Voice recognition deactivated');
    }
  }

  toggleAudio() {
    this.audioEnabled = !this.audioEnabled;
    this.preferences.audioEnabled = this.audioEnabled;
    this.savePreferences();

    if (this.audioEnabled) {
      this.initializeAudioSystem();
      this.playNavigationSound('click');
    }

    console.log(`🔊 Audio ${this.audioEnabled ? 'enabled' : 'disabled'}`);
  }

  async init() {
    console.log('🧬 Initializing Enhanced DNA Navigation System...');

    // Initialize preferences and systems
    this.initializeAudioSystem();
    this.initializeVoiceRecognition();
    this.setupKeyboardShortcuts();
    this.setupGestureSupport();

    // Create navigation container
    this.createNavigationContainer();

    if (typeof THREE === 'undefined' || !this.isWebGLSupported) {
      console.warn('WebGL not supported, using enhanced CSS fallback');
      await this.createEnhancedCSSFallback();
      return;
    }

    try {
      await this.setupScene();
      await this.createEnhancedDNAHelix();
      this.setupAdvancedInteractions();
      this.setupEnhancedScrollTracking();
      this.startPerformanceMonitoring();
      this.animate();
      this.setupResponsive();
      console.log('✅ Enhanced DNA Navigation initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Enhanced DNA Navigation:', error);
      await this.createEnhancedCSSFallback();
    }
  }

  createNavigationContainer() {
    // Remove existing container if present
    const existing = document.getElementById('dna-navigation');
    if (existing) existing.remove();

    const container = document.createElement('div');
    container.id = 'dna-navigation';
    container.className = 'dna-navigation-container';
    
    // Responsive positioning
    const containerStyles = this.getContainerStyles();
    container.style.cssText = containerStyles;
    
    document.body.appendChild(container);
    this.container = container;
  }

  getContainerStyles() {
    if (this.isMobile) {
      return `
        position: fixed;
        top: 60px;
        right: 10px;
        width: 60px;
        height: 200px;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 30px;
        transition: all 0.3s ease;
        transform: translateX(70px);
      `;
    } else if (this.isTablet) {
      return `
        position: fixed;
        top: 50%;
        right: 20px;
        width: 80px;
        height: 250px;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 40px;
        transform: translateY(-50%);
        transition: all 0.3s ease;
      `;
    } else {
      return `
        position: fixed;
        top: 50%;
        right: 30px;
        width: 100px;
        height: 300px;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50px;
        transform: translateY(-50%);
        transition: all 0.3s ease;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
      `;
    }
  }

  setupScene() {
    const containerRect = this.container.getBoundingClientRect();
    
    // Create scene
    this.scene = new THREE.Scene();
    
    // Create camera
    const aspect = containerRect.width / containerRect.height;
    this.camera = new THREE.PerspectiveCamera(50, aspect, 0.1, 100);
    this.camera.position.set(0, 0, 15);
    
    // Create renderer
    this.renderer = new THREE.WebGLRenderer({ 
      alpha: true, 
      antialias: !this.isMobile,
      powerPreference: this.isMobile ? 'low-power' : 'high-performance'
    });
    
    this.renderer.setSize(containerRect.width, containerRect.height);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, this.isMobile ? 1 : 2));
    this.renderer.setClearColor(0x000000, 0);
    
    this.container.appendChild(this.renderer.domElement);
    
    // Setup lighting
    this.setupLighting();
  }

  setupLighting() {
    // Ambient light
    const ambientLight = new THREE.AmbientLight(0x2563eb, 0.4);
    this.scene.add(ambientLight);
    
    // Directional light
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 5, 5);
    this.scene.add(directionalLight);
    
    // Point lights for medical theme
    const pointLight1 = new THREE.PointLight(0x0891b2, 0.6, 20);
    pointLight1.position.set(3, 3, 3);
    this.scene.add(pointLight1);
    
    const pointLight2 = new THREE.PointLight(0x2563eb, 0.4, 15);
    pointLight2.position.set(-3, -3, 3);
    this.scene.add(pointLight2);
  }

  async createEnhancedDNAHelix() {
    console.log('🧬 Creating enhanced DNA helix navigation structure...');

    this.dnaHelix = new THREE.Group();
    this.segments = [];
    this.particleSystems = [];

    const quality = this.performanceMonitor.adaptiveQuality;
    const helixHeight = (this.isMobile ? 8 : this.isTablet ? 10 : 12) * quality;
    const helixRadius = (this.isMobile ? 1.5 : this.isTablet ? 1.8 : 2) * quality;
    const segmentCount = this.sections.length;
    const segmentHeight = helixHeight / segmentCount;

    // Progressive loading of segments
    for (let i = 0; i < this.sections.length; i++) {
      const section = this.sections[i];
      const segment = await this.createEnhancedDNASegment(section, i, segmentHeight, helixRadius, helixHeight);
      this.segments.push(segment);
      this.dnaHelix.add(segment.group);

      // Small delay for progressive loading
      if (i < this.sections.length - 1) {
        await this.waitFor(100);
      }
    }

    // Add dynamic lighting system
    this.setupDynamicLighting();

    // Add ambient particle system
    this.createAmbientParticles();

    this.scene.add(this.dnaHelix);
    this.dnaHelix.position.y = 0;

    console.log(`✅ Created ${segmentCount} enhanced DNA segments with particles`);
  }

  async createEnhancedDNASegment(section, index, segmentHeight, helixRadius, totalHeight) {
    const segmentGroup = new THREE.Group();
    const yPosition = (index * segmentHeight) - (totalHeight / 2) + (segmentHeight / 2);

    // Create enhanced base pair strands with higher detail
    const turns = 0.5;
    const strandPoints = this.performanceMonitor.isLowPerformance ? 6 : 12;
    const strand1Points = [];
    const strand2Points = [];

    for (let i = 0; i <= strandPoints; i++) {
      const progress = i / strandPoints;
      const angle1 = progress * Math.PI * 2 * turns;
      const angle2 = angle1 + Math.PI;
      const y = yPosition + (progress - 0.5) * segmentHeight;

      strand1Points.push(new THREE.Vector3(
        Math.cos(angle1) * helixRadius,
        y,
        Math.sin(angle1) * helixRadius
      ));

      strand2Points.push(new THREE.Vector3(
        Math.cos(angle2) * helixRadius,
        y,
        Math.sin(angle2) * helixRadius
      ));
    }

    // Create enhanced geometries with object pooling
    const strand1Geometry = this.getPooledGeometry('tube', () =>
      new THREE.TubeGeometry(
        new THREE.CatmullRomCurve3(strand1Points),
        16, 0.1, 8, false
      )
    );

    const strand2Geometry = this.getPooledGeometry('tube', () =>
      new THREE.TubeGeometry(
        new THREE.CatmullRomCurve3(strand2Points),
        16, 0.1, 8, false
      )
    );

    // Create advanced materials with subsurface scattering effect
    const strand1Material = this.createAdvancedMaterial(section.color, 'strand1');
    const strand2Material = this.createAdvancedMaterial(section.color, 'strand2');

    // Create strand meshes
    const strand1Mesh = new THREE.Mesh(strand1Geometry, strand1Material);
    const strand2Mesh = new THREE.Mesh(strand2Geometry, strand2Material);

    segmentGroup.add(strand1Mesh);
    segmentGroup.add(strand2Mesh);

    // Create enhanced connecting base pairs
    const basePairCount = this.performanceMonitor.isLowPerformance ? 2 : 4;
    const basePairs = [];

    for (let i = 0; i < basePairCount; i++) {
      const progress = (i + 1) / (basePairCount + 1);
      const angle = progress * Math.PI * 2 * turns;
      const y = yPosition + (progress - 0.5) * segmentHeight;

      const basePairGeometry = this.getPooledGeometry('cylinder', () =>
        new THREE.CylinderGeometry(0.03, 0.03, helixRadius * 2, 6)
      );

      const basePairMaterial = this.createAdvancedMaterial(section.color, 'basePair');

      const basePair = new THREE.Mesh(basePairGeometry, basePairMaterial);
      basePair.position.set(0, y, 0);
      basePair.rotation.z = angle;

      basePairs.push(basePair);
      segmentGroup.add(basePair);
    }

    // Create particle system for this segment
    const particles = this.createSegmentParticles(section, yPosition, helixRadius);
    if (particles) {
      segmentGroup.add(particles);
    }

    return {
      group: segmentGroup,
      section: section,
      index: index,
      strand1Material: strand1Material,
      strand2Material: strand2Material,
      basePairs: basePairs,
      particles: particles,
      completed: this.preferences.completedSections.includes(section.id),
      progress: this.preferences.sectionProgress[section.id] || 0
    };
  }

  createAdvancedMaterial(color, type) {
    const materialKey = `${color}-${type}`;

    if (this.objectPool.materials.has(materialKey)) {
      return this.objectPool.materials.get(materialKey).clone();
    }

    const baseColor = new THREE.Color(color);

    // Create material with subsurface scattering effect
    const material = new THREE.MeshPhongMaterial({
      color: baseColor,
      transparent: true,
      opacity: type === 'basePair' ? 0.2 : 0.3,
      shininess: 100,
      specular: new THREE.Color(0x444444),
      emissive: new THREE.Color(color).multiplyScalar(0.1),
      side: THREE.DoubleSide
    });

    // Add dynamic properties for completion animation
    material.userData = {
      originalColor: baseColor.clone(),
      originalOpacity: material.opacity,
      originalEmissive: material.emissive.clone(),
      type: type
    };

    this.objectPool.materials.set(materialKey, material);
    return material.clone();
  }

  getPooledGeometry(type, createFn) {
    if (this.objectPool.geometries.has(type)) {
      return this.objectPool.geometries.get(type);
    }

    const geometry = createFn();
    this.objectPool.geometries.set(type, geometry);
    return geometry;
  }

  createSegmentParticles(section, yPosition, radius) {
    if (!this.preferences.particleEffects || this.performanceMonitor.isLowPerformance) {
      return null;
    }

    const particleCount = this.isMobile ? 10 : 20;
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);

    const color = new THREE.Color(section.color);

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;

      // Random position around the segment
      const angle = Math.random() * Math.PI * 2;
      const r = radius + (Math.random() - 0.5) * 2;
      const y = yPosition + (Math.random() - 0.5) * 2;

      positions[i3] = Math.cos(angle) * r;
      positions[i3 + 1] = y;
      positions[i3 + 2] = Math.sin(angle) * r;

      // Color variation
      colors[i3] = color.r + (Math.random() - 0.5) * 0.2;
      colors[i3 + 1] = color.g + (Math.random() - 0.5) * 0.2;
      colors[i3 + 2] = color.b + (Math.random() - 0.5) * 0.2;

      sizes[i] = Math.random() * 0.1 + 0.05;
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

    const material = new THREE.PointsMaterial({
      size: 0.1,
      transparent: true,
      opacity: 0.6,
      vertexColors: true,
      blending: THREE.AdditiveBlending,
      sizeAttenuation: true
    });

    const particles = new THREE.Points(geometry, material);
    particles.userData = {
      originalPositions: positions.slice(),
      animationPhase: Math.random() * Math.PI * 2
    };

    return particles;
  }

  createAmbientParticles() {
    if (!this.preferences.particleEffects || this.performanceMonitor.isLowPerformance) {
      return;
    }

    const particleCount = this.isMobile ? 50 : 100;
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;

      positions[i3] = (Math.random() - 0.5) * 20;
      positions[i3 + 1] = (Math.random() - 0.5) * 20;
      positions[i3 + 2] = (Math.random() - 0.5) * 20;

      // Medical-themed colors
      const medicalColors = [
        new THREE.Color(0x2563eb),
        new THREE.Color(0x0891b2),
        new THREE.Color(0x059669),
        new THREE.Color(0x7c3aed)
      ];

      const color = medicalColors[Math.floor(Math.random() * medicalColors.length)];
      colors[i3] = color.r;
      colors[i3 + 1] = color.g;
      colors[i3 + 2] = color.b;

      sizes[i] = Math.random() * 0.05 + 0.02;
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

    const material = new THREE.PointsMaterial({
      size: 0.03,
      transparent: true,
      opacity: 0.3,
      vertexColors: true,
      blending: THREE.AdditiveBlending,
      sizeAttenuation: true
    });

    const ambientParticles = new THREE.Points(geometry, material);
    ambientParticles.userData = {
      originalPositions: positions.slice(),
      rotationSpeed: 0.001
    };

    this.scene.add(ambientParticles);
    this.ambientParticles = ambientParticles;
  }

  setupDynamicLighting() {
    // Time-based lighting changes
    this.dynamicLights = {
      timeOfDay: 0,
      sectionLights: []
    };

    // Create section-specific lights
    this.sections.forEach((section, index) => {
      const light = new THREE.PointLight(section.color, 0.3, 10);
      const yPos = (index - this.sections.length / 2) * 2;
      light.position.set(3, yPos, 3);

      this.scene.add(light);
      this.dynamicLights.sectionLights.push(light);
    });
  }

  createDNASegment(section, index, segmentHeight, helixRadius, totalHeight) {
    const segmentGroup = new THREE.Group();
    const yPosition = (index * segmentHeight) - (totalHeight / 2) + (segmentHeight / 2);
    
    // Create base pair strands
    const turns = 0.5; // Half turn per segment
    const strandPoints = 8; // Points per segment
    const strand1Points = [];
    const strand2Points = [];
    
    for (let i = 0; i <= strandPoints; i++) {
      const progress = i / strandPoints;
      const angle1 = progress * Math.PI * 2 * turns;
      const angle2 = angle1 + Math.PI;
      const y = yPosition + (progress - 0.5) * segmentHeight;
      
      strand1Points.push(new THREE.Vector3(
        Math.cos(angle1) * helixRadius,
        y,
        Math.sin(angle1) * helixRadius
      ));
      
      strand2Points.push(new THREE.Vector3(
        Math.cos(angle2) * helixRadius,
        y,
        Math.sin(angle2) * helixRadius
      ));
    }
    
    // Create strand geometries
    const strand1Geometry = new THREE.TubeGeometry(
      new THREE.CatmullRomCurve3(strand1Points),
      16, 0.1, 8, false
    );
    
    const strand2Geometry = new THREE.TubeGeometry(
      new THREE.CatmullRomCurve3(strand2Points),
      16, 0.1, 8, false
    );
    
    // Create materials
    const strand1Material = new THREE.MeshPhongMaterial({
      color: section.color,
      transparent: true,
      opacity: 0.3, // Start transparent
      shininess: 100
    });
    
    const strand2Material = new THREE.MeshPhongMaterial({
      color: section.color,
      transparent: true,
      opacity: 0.3, // Start transparent
      shininess: 100
    });
    
    // Create strand meshes
    const strand1Mesh = new THREE.Mesh(strand1Geometry, strand1Material);
    const strand2Mesh = new THREE.Mesh(strand2Geometry, strand2Material);
    
    segmentGroup.add(strand1Mesh);
    segmentGroup.add(strand2Mesh);
    
    // Create connecting base pairs
    const basePairCount = 3;
    const basePairs = [];
    
    for (let i = 0; i < basePairCount; i++) {
      const progress = (i + 1) / (basePairCount + 1);
      const angle = progress * Math.PI * 2 * turns;
      const y = yPosition + (progress - 0.5) * segmentHeight;
      
      const basePairGeometry = new THREE.CylinderGeometry(0.03, 0.03, helixRadius * 2, 6);
      const basePairMaterial = new THREE.MeshPhongMaterial({
        color: section.color,
        transparent: true,
        opacity: 0.2
      });
      
      const basePair = new THREE.Mesh(basePairGeometry, basePairMaterial);
      basePair.position.set(0, y, 0);
      basePair.rotation.z = angle;
      
      basePairs.push(basePair);
      segmentGroup.add(basePair);
    }
    
    return {
      group: segmentGroup,
      section: section,
      index: index,
      strand1Material: strand1Material,
      strand2Material: strand2Material,
      basePairs: basePairs,
      completed: false,
      progress: 0
    };
  }

  setupInteractions() {
    // Add click handlers for navigation
    this.container.addEventListener('click', (event) => {
      const rect = this.container.getBoundingClientRect();
      const y = event.clientY - rect.top;
      const segmentIndex = Math.floor((y / rect.height) * this.sections.length);
      
      if (segmentIndex >= 0 && segmentIndex < this.sections.length) {
        this.navigateToSection(segmentIndex);
      }
    });
    
    // Add hover effects
    this.container.addEventListener('mousemove', (event) => {
      this.handleHover(event);
    });
    
    this.container.addEventListener('mouseleave', () => {
      this.clearHover();
    });
  }

  navigateToSection(sectionIndex) {
    const section = this.sections[sectionIndex];
    const targetElement = document.getElementById(section.id);

    if (targetElement) {
      console.log(`🧬 Navigating to section: ${section.name}`);

      // Track navigation analytics
      this.trackAnalytics('section_navigation', {
        fromSection: this.currentSection,
        toSection: sectionIndex,
        sectionName: section.name,
        navigationMethod: 'dna_click'
      });

      // Play navigation sound and haptic feedback
      this.playNavigationSound('click');
      this.triggerHapticFeedback('medium');

      // Add visual feedback to the segment
      this.addNavigationFeedback(sectionIndex);

      // Smooth scroll with enhanced easing
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });

      // Track engagement time for previous section
      this.trackSectionEngagement(this.currentSection);

      // Update current section
      this.setCurrentSection(sectionIndex);
    }
  }

  addNavigationFeedback(sectionIndex) {
    const segment = this.segments[sectionIndex];
    if (!segment) return;

    // Create ripple effect
    const originalScale = segment.group.scale.x;

    // Elastic animation
    const animate = () => {
      let progress = 0;
      const duration = 600;
      const startTime = Date.now();

      const tick = () => {
        progress = (Date.now() - startTime) / duration;

        if (progress < 1) {
          // Elastic easing function
          const elasticOut = (t) => {
            const c4 = (2 * Math.PI) / 3;
            return t === 0 ? 0 : t === 1 ? 1 :
              Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
          };

          const scale = originalScale + elasticOut(progress) * 0.3;
          segment.group.scale.setScalar(scale);

          requestAnimationFrame(tick);
        } else {
          segment.group.scale.setScalar(originalScale);
        }
      };

      tick();
    };

    animate();

    // Particle burst effect
    if (segment.particles && this.preferences.particleEffects) {
      this.createParticleBurst(segment);
    }
  }

  createParticleBurst(segment) {
    const burstCount = this.isMobile ? 10 : 20;
    const burstGeometry = new THREE.BufferGeometry();
    const positions = new Float32Array(burstCount * 3);
    const velocities = new Float32Array(burstCount * 3);
    const colors = new Float32Array(burstCount * 3);

    const segmentPosition = segment.group.position;
    const color = new THREE.Color(segment.section.color);

    for (let i = 0; i < burstCount; i++) {
      const i3 = i * 3;

      // Start at segment position
      positions[i3] = segmentPosition.x;
      positions[i3 + 1] = segmentPosition.y;
      positions[i3 + 2] = segmentPosition.z;

      // Random velocities
      velocities[i3] = (Math.random() - 0.5) * 2;
      velocities[i3 + 1] = (Math.random() - 0.5) * 2;
      velocities[i3 + 2] = (Math.random() - 0.5) * 2;

      // Bright colors
      colors[i3] = color.r;
      colors[i3 + 1] = color.g;
      colors[i3 + 2] = color.b;
    }

    burstGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    burstGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

    const burstMaterial = new THREE.PointsMaterial({
      size: 0.2,
      transparent: true,
      opacity: 1.0,
      vertexColors: true,
      blending: THREE.AdditiveBlending
    });

    const burstParticles = new THREE.Points(burstGeometry, burstMaterial);
    this.scene.add(burstParticles);

    // Animate burst
    let time = 0;
    const animateBurst = () => {
      time += 0.016; // ~60fps

      const positions = burstParticles.geometry.attributes.position.array;

      for (let i = 0; i < burstCount; i++) {
        const i3 = i * 3;

        positions[i3] += velocities[i3] * 0.1;
        positions[i3 + 1] += velocities[i3 + 1] * 0.1;
        positions[i3 + 2] += velocities[i3 + 2] * 0.1;
      }

      burstParticles.geometry.attributes.position.needsUpdate = true;
      burstParticles.material.opacity = Math.max(0, 1 - time * 2);

      if (time < 0.5) {
        requestAnimationFrame(animateBurst);
      } else {
        this.scene.remove(burstParticles);
        burstGeometry.dispose();
        burstMaterial.dispose();
      }
    };

    animateBurst();
  }

  trackSectionEngagement(sectionIndex) {
    if (sectionIndex < 0 || !this.sections[sectionIndex]) return;

    const section = this.sections[sectionIndex];
    const now = Date.now();

    if (this.sectionStartTime) {
      const engagementTime = now - this.sectionStartTime;
      this.analytics.sectionEngagementTime[section.id] =
        (this.analytics.sectionEngagementTime[section.id] || 0) + engagementTime;
    }

    this.sectionStartTime = now;
  }

  handleHover(event) {
    const rect = this.container.getBoundingClientRect();
    const y = event.clientY - rect.top;
    const segmentIndex = Math.floor((y / rect.height) * this.sections.length);
    
    if (segmentIndex >= 0 && segmentIndex < this.sections.length) {
      this.showTooltip(segmentIndex, event.clientX, event.clientY);
      this.highlightSegment(segmentIndex);
    }
  }

  showTooltip(sectionIndex, x, y) {
    // Remove existing tooltip
    const existingTooltip = document.getElementById('dna-tooltip');
    if (existingTooltip) existingTooltip.remove();

    const section = this.sections[sectionIndex];
    const segment = this.segments[sectionIndex];
    const tooltip = document.createElement('div');
    tooltip.id = 'dna-tooltip';
    tooltip.className = 'dna-tooltip enhanced';

    // Calculate progress percentage
    const progressPercent = Math.round(segment.progress * 100);
    const completionStatus = section.completed ? 'Completed' : `${progressPercent}% viewed`;
    const completionIcon = section.completed ? '✅' : progressPercent > 0 ? '📖' : '⏳';

    // Estimated reading time based on section content
    const readingTimes = {
      'career-paths': '8 min read',
      'campus': '6 min read',
      'testimonials': '4 min read',
      'safety-support': '5 min read',
      'about': '7 min read',
      'curriculum': '10 min read'
    };

    // Section previews
    const sectionPreviews = {
      'career-paths': 'Global recognition, FMGE preparation, career opportunities',
      'campus': 'Modern facilities, student life, accommodation details',
      'testimonials': 'Student experiences, success stories, reviews',
      'safety-support': 'Student safety, support services, emergency protocols',
      'about': 'University history, accreditation, academic excellence',
      'curriculum': 'MBBS program structure, clinical training, assessments'
    };

    // Engagement time tracking
    const engagementTime = this.analytics.sectionEngagementTime[section.id] || 0;
    const engagementMinutes = Math.floor(engagementTime / 60000);

    tooltip.innerHTML = `
      <div class="tooltip-content enhanced">
        <div class="tooltip-header">
          <h4>${section.name}</h4>
          <div class="section-color" style="background-color: ${section.color}"></div>
        </div>

        <div class="tooltip-stats">
          <div class="stat-item">
            <span class="stat-icon">${completionIcon}</span>
            <span class="stat-text">${completionStatus}</span>
          </div>

          <div class="stat-item">
            <span class="stat-icon">⏱️</span>
            <span class="stat-text">${readingTimes[section.id] || '5 min read'}</span>
          </div>

          ${engagementMinutes > 0 ? `
            <div class="stat-item">
              <span class="stat-icon">👁️</span>
              <span class="stat-text">${engagementMinutes}m engaged</span>
            </div>
          ` : ''}
        </div>

        <div class="tooltip-preview">
          <p>${sectionPreviews[section.id] || 'Click to explore this section'}</p>
        </div>

        <div class="tooltip-progress">
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${progressPercent}%; background-color: ${section.color}"></div>
          </div>
          <span class="progress-text">${progressPercent}%</span>
        </div>

        <div class="tooltip-actions">
          <span class="action-hint">Click to navigate • ${sectionIndex + 1} key</span>
        </div>
      </div>
    `;

    tooltip.style.cssText = `
      position: fixed;
      left: ${x - 160}px;
      top: ${y - 100}px;
      background: var(--glass-bg-strong);
      backdrop-filter: var(--glass-backdrop);
      -webkit-backdrop-filter: var(--glass-backdrop);
      border: 1px solid var(--glass-border);
      border-radius: 12px;
      padding: 16px;
      z-index: 10001;
      pointer-events: none;
      font-size: 12px;
      box-shadow: var(--shadow-multi-layer);
      transform: translateX(-50%);
      min-width: 280px;
      animation: tooltipSlideIn 0.3s ease-out;
    `;

    document.body.appendChild(tooltip);

    // Enhanced auto-remove with fade out
    setTimeout(() => {
      if (tooltip.parentNode) {
        tooltip.style.animation = 'tooltipSlideOut 0.3s ease-in forwards';
        setTimeout(() => tooltip.remove(), 300);
      }
    }, 5000);

    // Play hover sound
    this.playNavigationSound('hover');
  }

  clearHover() {
    const tooltip = document.getElementById('dna-tooltip');
    if (tooltip) tooltip.remove();
    
    // Reset segment highlighting
    this.segments.forEach(segment => {
      if (!segment.completed) {
        segment.strand1Material.opacity = 0.3;
        segment.strand2Material.opacity = 0.3;
      }
    });
  }

  highlightSegment(index) {
    this.segments.forEach((segment, i) => {
      if (i === index && !segment.completed) {
        segment.strand1Material.opacity = 0.6;
        segment.strand2Material.opacity = 0.6;
      } else if (!segment.completed) {
        segment.strand1Material.opacity = 0.3;
        segment.strand2Material.opacity = 0.3;
      }
    });
  }

  setupScrollTracking() {
    console.log('📜 Setting up scroll tracking for DNA navigation...');

    const observerOptions = {
      threshold: [0, 0.25, 0.5, 0.75, 1],
      rootMargin: '-10% 0px -10% 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const sectionId = entry.target.id;
        const sectionIndex = this.sections.findIndex(section => section.id === sectionId);

        if (sectionIndex !== -1) {
          const intersectionRatio = entry.intersectionRatio;
          this.updateSectionProgress(sectionIndex, intersectionRatio);

          if (intersectionRatio > 0.5) {
            this.setCurrentSection(sectionIndex);
          }
        }
      });
    }, observerOptions);

    // Observe all sections
    this.sections.forEach(section => {
      const element = document.getElementById(section.id);
      if (element) {
        observer.observe(element);
      } else {
        console.warn(`Section element not found: ${section.id}`);
      }
    });
  }

  updateSectionProgress(sectionIndex, progress) {
    if (sectionIndex < 0 || sectionIndex >= this.segments.length) return;

    const segment = this.segments[sectionIndex];
    segment.progress = progress;

    // Update visual progress
    const opacity = Math.max(0.3, progress);
    segment.strand1Material.opacity = opacity;
    segment.strand2Material.opacity = opacity;

    // Mark as completed if fully viewed
    if (progress > 0.8 && !segment.completed) {
      this.completeSection(sectionIndex);
    }

    // Update base pairs opacity
    segment.basePairs.forEach(basePair => {
      basePair.material.opacity = Math.max(0.2, progress * 0.6);
    });
  }

  completeSection(sectionIndex) {
    const segment = this.segments[sectionIndex];
    const section = this.sections[sectionIndex];

    segment.completed = true;
    section.completed = true;

    // Animate completion
    segment.strand1Material.opacity = 1.0;
    segment.strand2Material.opacity = 1.0;

    // Add completion glow effect
    segment.basePairs.forEach(basePair => {
      basePair.material.opacity = 0.8;
      basePair.material.emissive.setHex(0x004400);
    });

    console.log(`✅ DNA Navigation: Section "${section.name}" completed`);
  }

  setCurrentSection(sectionIndex) {
    if (this.currentSection !== sectionIndex) {
      this.currentSection = sectionIndex;

      // Update visual current section indicator
      this.segments.forEach((segment, index) => {
        if (index === sectionIndex) {
          // Highlight current section
          segment.group.scale.setScalar(1.1);
        } else {
          segment.group.scale.setScalar(1.0);
        }
      });
    }
  }

  animate() {
    this.animationId = requestAnimationFrame(() => this.animate());
    this.performanceMonitor.frameCount++;

    const time = Date.now() * 0.001;
    const animationSpeed = this.preferences.animationSpeed;

    // Gentle rotation of the entire helix
    if (this.dnaHelix) {
      this.dnaHelix.rotation.y = time * 0.1 * animationSpeed;

      // Subtle floating animation
      this.dnaHelix.position.y = Math.sin(time * 0.5 * animationSpeed) * 0.2;
    }

    // Animate individual segments with enhanced effects
    this.segments.forEach((segment, index) => {
      if (segment.completed) {
        // Completed segments have enhanced pulsing and glow
        const pulse = 1 + Math.sin(time * 2 + index) * 0.05;
        segment.group.scale.setScalar(pulse);

        // Dynamic glow effect
        const glowIntensity = 0.1 + Math.sin(time * 3 + index) * 0.05;
        segment.strand1Material.emissive.setScalar(glowIntensity);
        segment.strand2Material.emissive.setScalar(glowIntensity);
      }

      // Animate particles
      if (segment.particles && this.preferences.particleEffects) {
        this.animateSegmentParticles(segment.particles, time, index);
      }
    });

    // Animate ambient particles
    if (this.ambientParticles) {
      this.animateAmbientParticles(time);
    }

    // Update dynamic lighting
    this.updateDynamicLighting(time);

    // Update material gradients based on completion
    this.updateMaterialGradients(time);

    this.renderer.render(this.scene, this.camera);
  }

  animateSegmentParticles(particles, time, segmentIndex) {
    const positions = particles.geometry.attributes.position.array;
    const originalPositions = particles.userData.originalPositions;
    const phase = particles.userData.animationPhase;

    for (let i = 0; i < positions.length; i += 3) {
      const index = i / 3;
      const offset = (time + phase + index * 0.1) * 2;

      positions[i] = originalPositions[i] + Math.sin(offset) * 0.2;
      positions[i + 1] = originalPositions[i + 1] + Math.cos(offset * 0.7) * 0.1;
      positions[i + 2] = originalPositions[i + 2] + Math.sin(offset * 1.3) * 0.2;
    }

    particles.geometry.attributes.position.needsUpdate = true;

    // Fade particles based on segment completion
    const segment = this.segments[segmentIndex];
    if (segment) {
      particles.material.opacity = segment.completed ? 0.8 : 0.3;
    }
  }

  animateAmbientParticles(time) {
    const positions = this.ambientParticles.geometry.attributes.position.array;
    const originalPositions = this.ambientParticles.userData.originalPositions;
    const rotationSpeed = this.ambientParticles.userData.rotationSpeed;

    // Gentle rotation and floating
    this.ambientParticles.rotation.y += rotationSpeed;
    this.ambientParticles.position.y = Math.sin(time * 0.3) * 0.5;

    // Individual particle movement
    for (let i = 0; i < positions.length; i += 3) {
      const index = i / 3;
      const offset = time * 0.5 + index * 0.1;

      positions[i] = originalPositions[i] + Math.sin(offset) * 0.1;
      positions[i + 1] = originalPositions[i + 1] + Math.cos(offset * 0.8) * 0.1;
      positions[i + 2] = originalPositions[i + 2] + Math.sin(offset * 1.2) * 0.1;
    }

    this.ambientParticles.geometry.attributes.position.needsUpdate = true;
  }

  updateDynamicLighting(time) {
    if (!this.dynamicLights) return;

    // Time of day simulation (24 hour cycle in 2 minutes)
    this.dynamicLights.timeOfDay = (time * 0.5) % (Math.PI * 2);
    const dayIntensity = (Math.sin(this.dynamicLights.timeOfDay) + 1) * 0.5;

    // Update ambient light based on time of day
    if (this.scene.children.find(child => child.type === 'AmbientLight')) {
      const ambientLight = this.scene.children.find(child => child.type === 'AmbientLight');
      ambientLight.intensity = 0.2 + dayIntensity * 0.3;
    }

    // Update section lights based on current section
    this.dynamicLights.sectionLights.forEach((light, index) => {
      if (index === this.currentSection) {
        light.intensity = 0.5 + Math.sin(time * 2) * 0.1;
      } else {
        light.intensity = 0.2;
      }
    });
  }

  updateMaterialGradients(time) {
    this.segments.forEach((segment, index) => {
      const progress = segment.progress;
      const completed = segment.completed;

      // Dynamic color gradients based on completion
      if (completed) {
        const hue = (time * 0.1 + index * 0.2) % 1;
        const saturation = 0.8;
        const lightness = 0.6 + Math.sin(time * 2 + index) * 0.1;

        const dynamicColor = new THREE.Color().setHSL(hue, saturation, lightness);
        segment.strand1Material.color.lerp(dynamicColor, 0.02);
        segment.strand2Material.color.lerp(dynamicColor, 0.02);
      } else {
        // Restore original color
        const originalColor = segment.strand1Material.userData.originalColor;
        segment.strand1Material.color.lerp(originalColor, 0.05);
        segment.strand2Material.color.lerp(originalColor, 0.05);
      }

      // Update opacity based on progress
      const targetOpacity = completed ? 1.0 : Math.max(0.3, progress);
      segment.strand1Material.opacity = THREE.MathUtils.lerp(
        segment.strand1Material.opacity,
        targetOpacity,
        0.05
      );
      segment.strand2Material.opacity = THREE.MathUtils.lerp(
        segment.strand2Material.opacity,
        targetOpacity,
        0.05
      );
    });
  }

  setupResponsive() {
    window.addEventListener('resize', () => {
      const wasMobile = this.isMobile;
      const wasTablet = this.isTablet;

      this.isMobile = window.innerWidth < 768;
      this.isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;

      if (wasMobile !== this.isMobile || wasTablet !== this.isTablet) {
        this.updateResponsiveLayout();
      }

      if (this.renderer && this.camera) {
        const containerRect = this.container.getBoundingClientRect();
        this.camera.aspect = containerRect.width / containerRect.height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(containerRect.width, containerRect.height);
      }
    });
  }

  updateResponsiveLayout() {
    // Update container styles
    this.container.style.cssText = this.getContainerStyles();

    // Recreate DNA helix with new dimensions
    if (this.dnaHelix) {
      this.scene.remove(this.dnaHelix);
      this.createDNAHelix();
    }
  }

  createCSSFallback() {
    console.log('🧬 Creating CSS fallback for DNA navigation...');

    this.container.innerHTML = `
      <div class="dna-fallback">
        ${this.sections.map((section, index) => `
          <div class="dna-segment" data-section="${section.id}" data-index="${index}">
            <div class="segment-indicator" style="background-color: ${section.color}"></div>
            <div class="segment-progress"></div>
          </div>
        `).join('')}
      </div>
    `;

    this.setupCSSFallbackStyles();
    this.setupCSSFallbackInteractions();
  }

  setupCSSFallbackStyles() {
    if (document.getElementById('dna-fallback-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'dna-fallback-styles';
    styles.textContent = `
      .dna-fallback {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 10px;
        gap: 8px;
      }

      .dna-segment {
        flex: 1;
        position: relative;
        cursor: pointer;
        border-radius: 20px;
        overflow: hidden;
        transition: all 0.3s ease;
      }

      .segment-indicator {
        width: 100%;
        height: 100%;
        opacity: 0.3;
        transition: all 0.3s ease;
        border-radius: 20px;
      }

      .segment-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: rgba(255, 255, 255, 0.8);
        transition: width 0.3s ease;
      }

      .dna-segment:hover .segment-indicator {
        opacity: 0.6;
        transform: scale(1.05);
      }

      .dna-segment.completed .segment-indicator {
        opacity: 1;
      }

      .dna-segment.current {
        transform: scale(1.1);
      }
    `;
    document.head.appendChild(styles);
  }

  setupCSSFallbackInteractions() {
    const segments = this.container.querySelectorAll('.dna-segment');

    segments.forEach((segment, index) => {
      segment.addEventListener('click', () => {
        this.navigateToSection(index);
      });

      segment.addEventListener('mouseenter', () => {
        const rect = segment.getBoundingClientRect();
        this.showTooltip(index, rect.right + 10, rect.top + rect.height / 2);
      });
    });

    // Setup scroll tracking for CSS fallback
    this.setupScrollTrackingCSS();
  }

  setupScrollTrackingCSS() {
    const observerOptions = {
      threshold: [0, 0.25, 0.5, 0.75, 1],
      rootMargin: '-10% 0px -10% 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const sectionId = entry.target.id;
        const sectionIndex = this.sections.findIndex(section => section.id === sectionId);

        if (sectionIndex !== -1) {
          const segment = this.container.querySelector(`[data-index="${sectionIndex}"]`);
          if (segment) {
            const progress = entry.intersectionRatio;
            const progressBar = segment.querySelector('.segment-progress');

            progressBar.style.width = `${progress * 100}%`;

            if (progress > 0.8) {
              segment.classList.add('completed');
              this.sections[sectionIndex].completed = true;
            }

            if (progress > 0.5) {
              // Remove current class from all segments
              this.container.querySelectorAll('.dna-segment').forEach(s => s.classList.remove('current'));
              segment.classList.add('current');
            }
          }
        }
      });
    }, observerOptions);

    this.sections.forEach(section => {
      const element = document.getElementById(section.id);
      if (element) {
        observer.observe(element);
      }
    });
  }

  dispose() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }

    if (this.renderer) {
      this.renderer.dispose();
    }

    if (this.container && this.container.parentNode) {
      this.container.remove();
    }
  }

  // Public methods for external control
  showNavigation() {
    if (this.isMobile) {
      this.container.style.transform = 'translateX(0)';
    }
  }

  hideNavigation() {
    if (this.isMobile) {
      this.container.style.transform = 'translateX(70px)';
    }
  }

  toggleNavigation() {
    if (!this.container) {
      console.warn('DNA navigation container not found');
      return;
    }

    if (this.isMobile) {
      const isHidden = this.container.style.transform.includes('translateX(70px)');
      if (isHidden) {
        this.showNavigation();
      } else {
        this.hideNavigation();
      }
    }
  }

  showNavigation() {
    if (this.container) {
      this.container.style.transform = 'translateX(0)';
      this.container.style.opacity = '1';
    }
  }

  hideNavigation() {
    if (this.container) {
      this.container.style.transform = 'translateX(70px)';
      this.container.style.opacity = '0.8';
    }
  }

  // ===== MISSING METHOD IMPLEMENTATIONS =====

  setupAdvancedInteractions() {
    // Add click handlers for navigation
    this.container.addEventListener('click', (event) => {
      const rect = this.container.getBoundingClientRect();
      const y = event.clientY - rect.top;
      const segmentIndex = Math.floor((y / rect.height) * this.sections.length);

      if (segmentIndex >= 0 && segmentIndex < this.sections.length) {
        this.navigateToSection(segmentIndex);
      }
    });

    // Add hover effects
    this.container.addEventListener('mousemove', (event) => {
      this.handleHover(event);
    });

    this.container.addEventListener('mouseleave', () => {
      this.clearHover();
    });
  }

  // Alias for backward compatibility
  setupInteractions() {
    return this.setupAdvancedInteractions();
  }

  setupEnhancedScrollTracking() {
    console.log('📜 Setting up enhanced scroll tracking for DNA navigation...');

    const observerOptions = {
      threshold: [0, 0.25, 0.5, 0.75, 1],
      rootMargin: '-10% 0px -10% 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const sectionId = entry.target.id;
        const sectionIndex = this.sections.findIndex(section => section.id === sectionId);

        if (sectionIndex !== -1) {
          const intersectionRatio = entry.intersectionRatio;
          this.updateSectionProgress(sectionIndex, intersectionRatio);

          if (intersectionRatio > 0.5) {
            this.setCurrentSection(sectionIndex);
          }
        }
      });
    }, observerOptions);

    // Observe all sections
    this.sections.forEach(section => {
      const element = document.getElementById(section.id);
      if (element) {
        observer.observe(element);
      } else {
        console.warn(`Section element not found: ${section.id}`);
      }
    });
  }

  // Alias for backward compatibility
  setupScrollTracking() {
    return this.setupEnhancedScrollTracking();
  }

  adjustParticleCount(particles, targetCount) {
    if (!particles || !particles.geometry) return;

    const currentCount = particles.geometry.attributes.position.count;

    if (currentCount !== targetCount) {
      // Create new geometry with target count
      const newGeometry = new THREE.BufferGeometry();
      const positions = new Float32Array(targetCount * 3);
      const colors = new Float32Array(targetCount * 3);
      const sizes = new Float32Array(targetCount);

      // Copy existing data up to the minimum count
      const copyCount = Math.min(currentCount, targetCount);
      const oldPositions = particles.geometry.attributes.position.array;
      const oldColors = particles.geometry.attributes.color.array;
      const oldSizes = particles.geometry.attributes.size.array;

      for (let i = 0; i < copyCount * 3; i++) {
        positions[i] = oldPositions[i];
        colors[i] = oldColors[i];
      }

      for (let i = 0; i < copyCount; i++) {
        sizes[i] = oldSizes[i];
      }

      // Fill remaining with random data if expanding
      for (let i = copyCount; i < targetCount; i++) {
        const i3 = i * 3;
        positions[i3] = (Math.random() - 0.5) * 4;
        positions[i3 + 1] = (Math.random() - 0.5) * 4;
        positions[i3 + 2] = (Math.random() - 0.5) * 4;

        colors[i3] = Math.random();
        colors[i3 + 1] = Math.random();
        colors[i3 + 2] = Math.random();

        sizes[i] = Math.random() * 0.1 + 0.05;
      }

      newGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      newGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
      newGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

      // Replace geometry
      particles.geometry.dispose();
      particles.geometry = newGeometry;
    }
  }
}

// Register class globally and close protection block
window.DNANavigation = DNANavigation;

// Auto-initialize DNA Navigation when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Wait for other components to initialize
  setTimeout(() => {
    console.log('🧬 Initializing DNA Navigation System...');
    window.dnaNavigation = new DNANavigation();
  }, 2000);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DNANavigation;
}

} // Close class protection block
