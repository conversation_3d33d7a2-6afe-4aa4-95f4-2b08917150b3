/* ===== MODERN COMPONENT STYLES ===== */
/* Enhanced UI Components for Medical Education Platform */

/* ===== SECTION LAYOUT ===== */
.section-padding {
  padding: var(--space-24) var(--space-6);
  max-width: var(--container-7xl);
  margin: 0 auto;
  position: relative;
}

.section-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  text-align: center;
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -var(--space-4);
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  border-radius: var(--radius-full);
}

.section-description {
  text-align: center;
  font-size: var(--text-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto var(--space-16);
  line-height: var(--leading-relaxed);
}

/* ===== ENHANCED CAMPUS GALLERY ===== */
.simple-gallery {
  padding: var(--space-8) 0;
}

.gallery-header {
  text-align: center;
  margin-bottom: var(--space-12);
}

.gallery-header h3 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.gallery-header p {
  font-size: var(--text-lg);
  color: var(--color-text-secondary);
  max-width: 500px;
  margin: 0 auto;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

.gallery-item {
  background: var(--color-surface);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-default);
  cursor: pointer;
  position: relative;
  group: gallery-item;
}

.gallery-item:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.gallery-item img {
  width: 100%;
  height: 240px;
  object-fit: cover;
  transition: var(--transition-slow);
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-item-info {
  padding: var(--space-6);
  position: relative;
}

.gallery-item-info h4 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--color-primary);
  margin-bottom: var(--space-2);
}

.gallery-item-info p {
  color: var(--color-text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: 0;
}

.gallery-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  opacity: 0;
  transition: var(--transition-default);
  z-index: 1;
}

.gallery-item:hover::before {
  opacity: 0.1;
}

/* ===== ENHANCED ADVANTAGES SECTION ===== */
.simple-advantages {
  padding: var(--space-8) 0;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-8);
}

.advantage-item {
  background: var(--color-surface);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  text-align: center;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-default);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--color-border);
}

.advantage-item:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-200);
}

.advantage-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  transform: scaleX(0);
  transition: var(--transition-default);
}

.advantage-item:hover::before {
  transform: scaleX(1);
}

.advantage-icon {
  width: 100px;
  height: 100px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-4xl);
  margin: 0 auto var(--space-6);
  color: white;
  position: relative;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-default);
}

.advantage-item:hover .advantage-icon {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

.advantage-item h4 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

.advantage-item p {
  color: var(--color-text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: 0;
}

/* ===== ENHANCED APPLY BUTTON ===== */
.simple-apply-button {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  border-radius: var(--radius-3xl);
  padding: var(--space-16) var(--space-8);
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
}

.simple-apply-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
  opacity: 0.5;
}

.apply-content {
  position: relative;
  z-index: 2;
}

.apply-icon {
  font-size: var(--text-6xl);
  margin-bottom: var(--space-6);
  color: var(--secondary-400);
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.apply-content h3 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-4);
  color: white;
}

.apply-content p {
  font-size: var(--text-xl);
  margin-bottom: var(--space-8);
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  color: white;
}

.apply-btn {
  background: var(--secondary-500);
  color: var(--primary-900);
  border: none;
  padding: var(--space-5) var(--space-10);
  border-radius: var(--radius-full);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  cursor: pointer;
  transition: var(--transition-default);
  display: inline-flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-8);
  box-shadow: var(--shadow-lg);
  min-height: 56px;
}

.apply-btn:hover {
  background: var(--secondary-400);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.apply-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.5), var(--shadow-xl);
}

.apply-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.feature {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  padding: var(--space-3) var(--space-4);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-full);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature i {
  color: var(--success-400);
  font-size: var(--text-lg);
}

/* ===== ENHANCED MODAL ===== */
.simple-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

.simple-modal .modal-content {
  background: var(--color-surface);
  border-radius: var(--radius-2xl);
  max-width: 90vw;
  max-height: 90vh;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.simple-modal img {
  width: 100%;
  max-height: 60vh;
  object-fit: cover;
}

.simple-modal .modal-info {
  padding: var(--space-8);
}

.simple-modal .modal-info h3 {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.simple-modal .modal-info p {
  color: var(--color-text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: 0;
}

.modal-close {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  width: 44px;
  height: 44px;
  border-radius: var(--radius-full);
  font-size: var(--text-xl);
  cursor: pointer;
  z-index: var(--z-popover);
  transition: var(--transition-default);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
}

.modal-close:hover,
.modal-close:focus {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
  outline: none;
}

/* ===== LOADING SPINNER ENHANCEMENT ===== */
.component-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: var(--color-surface);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--color-border);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--primary-100);
  border-top: 4px solid var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

.component-loading::after {
  content: 'Loading...';
  font-size: var(--text-sm);
  color: var(--color-text-muted);
  font-weight: var(--font-medium);
}

/* ===== ABOUT FEFU SECTION ===== */
.about-fefu {
  background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
}

.about-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
}

.about-card {
  background: var(--color-surface);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  text-align: center;
  transition: var(--transition-default);
}

.about-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.about-icon {
  font-size: var(--text-5xl);
  margin-bottom: var(--space-4);
}

.about-card h3 {
  color: var(--color-primary);
  margin-bottom: var(--space-4);
  font-size: var(--text-xl);
}

.about-card ul {
  list-style: none;
  padding: 0;
  text-align: left;
}

.about-card li {
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--color-border);
  position: relative;
  padding-left: var(--space-6);
}

.about-card li:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--color-success);
  font-weight: var(--font-bold);
}

.about-card li:last-child {
  border-bottom: none;
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials-section {
  background: linear-gradient(135deg, var(--success-50), var(--primary-50));
}

.testimonials-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

.testimonial-card {
  background: var(--color-surface);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-default);
  border: 1px solid var(--color-border);
}

.testimonial-card.featured {
  border: 2px solid var(--color-primary);
  transform: scale(1.02);
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.testimonial-card.featured:hover {
  transform: translateY(-4px) scale(1.02);
}

.testimonial-header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.student-photo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--color-primary);
}

.student-info h3 {
  color: var(--color-primary);
  margin-bottom: var(--space-1);
  font-size: var(--text-xl);
}

.student-info p {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
}

.student-location {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  color: var(--color-text-muted);
  font-size: var(--text-xs);
}

.student-location i {
  color: var(--color-primary);
}

.testimonial-content {
  position: relative;
}

.quote-icon {
  font-size: var(--text-6xl);
  color: var(--primary-200);
  position: absolute;
  top: -var(--space-4);
  left: -var(--space-2);
  font-family: serif;
  line-height: 1;
}

.testimonial-content p {
  font-style: italic;
  line-height: var(--leading-relaxed);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-6);
  position: relative;
  z-index: 2;
}

.testimonial-stats {
  display: flex;
  gap: var(--space-6);
  padding-top: var(--space-4);
  border-top: 1px solid var(--color-border);
}

.testimonial-stats .stat {
  text-align: center;
}

.testimonial-stats .stat-number {
  display: block;
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--color-success);
}

.testimonial-stats .stat-label {
  font-size: var(--text-xs);
  color: var(--color-text-muted);
}

.success-metrics {
  background: var(--color-surface);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  text-align: center;
}

.success-metrics h3 {
  color: var(--color-primary);
  margin-bottom: var(--space-6);
  font-size: var(--text-2xl);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-6);
}

.metric-item {
  padding: var(--space-4);
  border-radius: var(--radius-xl);
  background: var(--primary-50);
  border: 1px solid var(--primary-200);
}

.metric-number {
  display: block;
  font-size: var(--text-4xl);
  font-weight: var(--font-extrabold);
  color: var(--color-primary);
  line-height: 1;
  margin-bottom: var(--space-2);
}

.metric-label {
  font-size: var(--text-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-medium);
}

/* ===== SAFETY & SUPPORT SECTION ===== */
.safety-section {
  background: var(--color-surface);
}

.safety-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

.safety-card {
  background: var(--color-surface);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-border);
  transition: var(--transition-default);
  text-align: center;
}

.safety-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--success-200);
}

.safety-icon {
  font-size: var(--text-5xl);
  margin-bottom: var(--space-4);
}

.safety-card h3 {
  color: var(--color-primary);
  margin-bottom: var(--space-4);
  font-size: var(--text-xl);
}

.safety-card ul {
  list-style: none;
  padding: 0;
  text-align: left;
}

.safety-card li {
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--color-border);
  position: relative;
  padding-left: var(--space-6);
  color: var(--color-text-secondary);
}

.safety-card li:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--color-success);
  font-weight: var(--font-bold);
}

.safety-card li:last-child {
  border-bottom: none;
}

.parent-testimonial {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-8);
  background: var(--success-50);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--success-200);
}

.parent-quote {
  background: var(--color-surface);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  position: relative;
}

.quote-content .quote-icon {
  font-size: var(--text-5xl);
  color: var(--success-200);
  position: absolute;
  top: var(--space-4);
  left: var(--space-4);
  font-family: serif;
}

.quote-content p {
  font-style: italic;
  line-height: var(--leading-relaxed);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
  padding-left: var(--space-8);
}

.quote-author {
  text-align: right;
  padding-left: var(--space-8);
}

.quote-author strong {
  display: block;
  color: var(--color-primary);
  font-size: var(--text-lg);
  margin-bottom: var(--space-1);
}

.quote-author span {
  color: var(--color-text-muted);
  font-size: var(--text-sm);
}

.emergency-contacts {
  background: var(--color-surface);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.emergency-contacts h4 {
  color: var(--color-primary);
  text-align: center;
  margin-bottom: var(--space-4);
  font-size: var(--text-lg);
}

.contact-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--error-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--error-200);
}

.contact-icon {
  font-size: var(--text-xl);
}

.contact-info {
  display: flex;
  flex-direction: column;
}

.contact-label {
  font-size: var(--text-sm);
  color: var(--color-text-muted);
  font-weight: var(--font-medium);
}

.contact-number {
  font-size: var(--text-base);
  color: var(--color-primary);
  font-weight: var(--font-semibold);
}

/* ===== ADMISSION PROCESS SECTION ===== */
.admission-section {
  background: var(--color-surface);
}

.admission-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  margin-top: var(--space-12);
}

.process-timeline {
  position: relative;
}

.process-timeline::before {
  content: '';
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--primary-200);
}

.timeline-item {
  display: flex;
  margin-bottom: var(--space-8);
  position: relative;
}

.timeline-number {
  width: 60px;
  height: 60px;
  background: var(--color-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  margin-right: var(--space-6);
  position: relative;
  z-index: 2;
  box-shadow: var(--shadow-lg);
}

.timeline-content {
  flex: 1;
  background: var(--color-surface);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border);
}

.timeline-content h3 {
  color: var(--color-primary);
  margin-bottom: var(--space-3);
  font-size: var(--text-lg);
}

.timeline-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.timeline-content li {
  padding: var(--space-1) 0;
  color: var(--color-text-secondary);
  position: relative;
  padding-left: var(--space-5);
}

.timeline-content li:before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--color-primary);
  font-weight: var(--font-bold);
}

.documents-checklist {
  background: var(--primary-50);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--primary-200);
}

.documents-checklist h3 {
  color: var(--color-primary);
  text-align: center;
  margin-bottom: var(--space-6);
  font-size: var(--text-2xl);
}

.checklist-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  margin-bottom: var(--space-8);
}

.checklist-column h4 {
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
  font-size: var(--text-lg);
  border-bottom: 2px solid var(--color-primary);
  padding-bottom: var(--space-2);
}

.checklist-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) 0;
  cursor: pointer;
  transition: var(--transition-default);
}

.checklist-item:hover {
  background: rgba(37, 99, 235, 0.05);
  border-radius: var(--radius-md);
  padding-left: var(--space-2);
}

.checklist-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--color-primary);
}

.important-dates {
  background: var(--color-surface);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.important-dates h4 {
  color: var(--color-primary);
  text-align: center;
  margin-bottom: var(--space-4);
  font-size: var(--text-lg);
}

.dates-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-4);
}

.date-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-3);
  background: var(--primary-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--primary-200);
}

.date-item .date {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--color-primary);
}

.date-item .event {
  font-size: var(--text-sm);
  color: var(--color-text-secondary);
  text-align: center;
}

/* ===== FAQ SECTION ===== */
.faq-section {
  background: var(--gray-50);
}

.faq-container {
  max-width: var(--container-4xl);
  margin: 0 auto;
}

.faq-item {
  background: var(--color-surface);
  border-radius: var(--radius-xl);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border);
  overflow: hidden;
  transition: var(--transition-default);
}

.faq-item:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-200);
}

.faq-question {
  width: 100%;
  background: none;
  border: none;
  padding: var(--space-6) var(--space-8);
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--color-text-primary);
  transition: var(--transition-default);
}

.faq-question:hover {
  background: var(--primary-50);
  color: var(--color-primary);
}

.faq-question i {
  color: var(--color-primary);
  transition: var(--transition-default);
  font-size: var(--text-base);
}

.faq-item.active .faq-question i {
  transform: rotate(180deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
  background: var(--primary-50);
}

.faq-item.active .faq-answer {
  max-height: 200px;
  padding: 0 var(--space-8) var(--space-6);
}

.faq-answer p {
  color: var(--color-text-secondary);
  line-height: var(--leading-relaxed);
  margin: 0;
  padding-top: var(--space-4);
}

/* ===== CURRICULUM SECTION ===== */
.curriculum-section {
  background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
}

.curriculum-overview {
  text-align: center;
  margin-bottom: var(--space-12);
  padding: var(--space-8);
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-6);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--text-4xl);
  font-weight: var(--font-extrabold);
  color: var(--color-primary);
  line-height: 1;
  margin-bottom: var(--space-2);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-medium);
}

.curriculum-timeline {
  display: grid;
  gap: var(--space-8);
}

.year-card {
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  transition: all var(--duration-300) var(--ease-premium);
}

.year-header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.year-number {
  width: 60px;
  height: 60px;
  background: var(--color-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  box-shadow: var(--shadow-lg);
}

.year-header h3 {
  color: var(--color-primary);
  margin: 0;
  font-size: var(--text-xl);
}

.year-focus {
  background: var(--success-100);
  color: var(--success-800);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.subject-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--primary-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--primary-200);
}

.subject-item i {
  color: var(--color-primary);
  font-size: var(--text-lg);
}

.year-highlights {
  background: var(--color-surface);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--color-primary);
}

.year-highlights p {
  margin: var(--space-2) 0;
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
}

.assessment-system {
  margin-top: var(--space-12);
  padding: var(--space-8);
  text-align: center;
}

.assessment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-6);
}

.assessment-item {
  text-align: center;
  padding: var(--space-6);
  background: var(--color-surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.assessment-icon {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-4);
}

.assessment-item h4 {
  color: var(--color-primary);
  margin-bottom: var(--space-3);
  font-size: var(--text-lg);
}

.assessment-item p {
  color: var(--color-text-secondary);
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
}

/* ===== CLINICAL TRAINING SECTION ===== */
.clinical-section {
  background: var(--color-surface);
}

.training-overview {
  text-align: center;
  margin-bottom: var(--space-12);
  padding: var(--space-8);
}

.training-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-6);
}

.hospitals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

.hospital-card {
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  transition: all var(--duration-300) var(--ease-premium);
}

.hospital-header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.hospital-icon {
  font-size: var(--text-4xl);
}

.hospital-header h3 {
  color: var(--color-primary);
  margin: 0;
  font-size: var(--text-xl);
}

.hospital-type {
  background: var(--secondary-100);
  color: var(--secondary-800);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.hospital-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2);
  background: var(--primary-50);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
}

.feature-item i {
  color: var(--color-primary);
}

.rotation-schedule {
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
}

.rotation-timeline {
  display: grid;
  gap: var(--space-8);
}

.rotation-year h4 {
  color: var(--color-primary);
  margin-bottom: var(--space-4);
  font-size: var(--text-lg);
}

.rotation-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.rotation-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-4);
  background: var(--success-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--success-200);
}

.rotation-duration {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--success-700);
}

.rotation-dept {
  font-size: var(--text-sm);
  color: var(--color-text-secondary);
  text-align: center;
}

/* ===== CAREER PATHS SECTION ===== */
.career-section {
  background: linear-gradient(135deg, var(--success-50), var(--primary-50));
}

.recognition-overview {
  text-align: center;
  margin-bottom: var(--space-12);
  padding: var(--space-8);
}

.recognition-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-6);
}

.countries-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

.country-card {
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  transition: all var(--duration-300) var(--ease-premium);
}

.country-header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.country-flag {
  font-size: var(--text-4xl);
}

.country-header h3 {
  color: var(--color-primary);
  margin: 0;
  font-size: var(--text-xl);
}

.recognition-status {
  background: var(--success-100);
  color: var(--success-800);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.country-content {
  display: grid;
  gap: var(--space-6);
}

.requirements h4,
.support-services h4 {
  color: var(--color-primary);
  margin-bottom: var(--space-3);
  font-size: var(--text-base);
}

.requirements ul,
.support-services ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirements li,
.support-services li {
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--color-border);
  position: relative;
  padding-left: var(--space-6);
  font-size: var(--text-sm);
}

.requirements li:before {
  content: '📋';
  position: absolute;
  left: 0;
}

.support-services li:before {
  content: '✅';
  position: absolute;
  left: 0;
}

.requirements li:last-child,
.support-services li:last-child {
  border-bottom: none;
}

.success-metrics {
  background: var(--success-50);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border: 1px solid var(--success-200);
}

.success-metrics p {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--success-800);
  font-weight: var(--font-medium);
}

.alumni-success {
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  text-align: center;
}

.alumni-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-6);
  margin: var(--space-8) 0;
}

.alumni-stat {
  text-align: center;
}

.alumni-number {
  display: block;
  font-size: var(--text-3xl);
  font-weight: var(--font-extrabold);
  color: var(--color-primary);
  line-height: 1;
  margin-bottom: var(--space-2);
}

.alumni-label {
  font-size: var(--text-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-medium);
}

.career-support-services {
  margin-top: var(--space-8);
}

.career-support-services h4 {
  color: var(--color-primary);
  text-align: center;
  margin-bottom: var(--space-6);
  font-size: var(--text-lg);
}

.support-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-4);
}

.support-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background: var(--primary-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--primary-200);
  text-align: center;
}

.support-item i {
  color: var(--color-primary);
  font-size: var(--text-xl);
}

.support-item span {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

/* ===== ENHANCED CAMPUS LIFE SECTION ===== */
.campus-life-section {
  background: var(--color-surface);
}

.campus-overview {
  text-align: center;
  margin-bottom: var(--space-8);
  padding: var(--space-8);
}

.campus-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-6);
}

.facilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  margin: var(--space-8) 0;
}

.facility-card {
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  transition: all var(--duration-300) var(--ease-premium);
}

.facility-header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.facility-icon {
  font-size: var(--text-4xl);
}

.facility-header h3 {
  color: var(--color-primary);
  margin: 0;
  font-size: var(--text-xl);
}

.facility-type {
  background: var(--secondary-100);
  color: var(--secondary-800);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.facility-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.facility-details {
  background: var(--color-surface);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--color-primary);
}

.facility-details p {
  margin: var(--space-2) 0;
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
}

.virtual-tour {
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  margin-top: var(--space-12);
}

.tour-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  align-items: center;
}

.tour-preview {
  background: var(--gray-100);
  border-radius: var(--radius-xl);
  aspect-ratio: 16/9;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--gray-300);
}

.tour-placeholder {
  text-align: center;
  color: var(--color-text-secondary);
}

.tour-placeholder i {
  font-size: var(--text-6xl);
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.tour-features h4 {
  color: var(--color-primary);
  margin-bottom: var(--space-4);
  font-size: var(--text-xl);
}

.tour-features ul {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--space-6) 0;
}

.tour-features li {
  padding: var(--space-2) 0;
  position: relative;
  padding-left: var(--space-6);
  font-size: var(--text-sm);
}

.tour-features li:before {
  content: '🎥';
  position: absolute;
  left: 0;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .section-padding {
    padding: var(--space-20) var(--space-4);
  }

  .about-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .admission-content {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }

  .countries-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .hospitals-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .facilities-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .tour-content {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
  }

  .advantages-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: var(--text-3xl);
  }

  .section-description {
    font-size: var(--text-base);
  }

  .overview-stats,
  .training-stats,
  .recognition-stats,
  .campus-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }

  .subjects-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .assessment-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .hospital-features,
  .facility-features {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .rotation-items {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .alumni-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }

  .support-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3);
  }

  .year-number {
    width: 50px;
    height: 50px;
    font-size: var(--text-lg);
  }

  .checklist-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .dates-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .process-timeline::before {
    left: 20px;
  }

  .timeline-number {
    width: 40px;
    height: 40px;
    font-size: var(--text-base);
    margin-right: var(--space-4);
  }

  .gallery-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .advantages-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .apply-features {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .simple-apply-button {
    padding: var(--space-12) var(--space-4);
  }

  .apply-content h3 {
    font-size: var(--text-3xl);
  }
  
  .apply-content p {
    font-size: var(--text-lg);
  }
  
  .apply-btn {
    font-size: var(--text-lg);
    padding: var(--space-4) var(--space-8);
  }
}

@media (max-width: 640px) {
  .section-padding {
    padding: var(--space-16) var(--space-2);
  }
  
  .advantage-item {
    padding: var(--space-6);
  }
  
  .advantage-icon {
    width: 80px;
    height: 80px;
    font-size: var(--text-3xl);
  }
  
  .simple-modal .modal-content {
    margin: var(--space-4);
    max-width: calc(100vw - 2rem);
  }
  
  .simple-modal .modal-info {
    padding: var(--space-6);
  }
}
