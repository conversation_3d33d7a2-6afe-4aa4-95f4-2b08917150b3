/* ===== DNA NAVIGATION TESTING SUITE ===== */
/* Comprehensive testing for DNA navigation system */

class DNANavigationTester {
  constructor() {
    this.testResults = [];
    this.init();
  }

  init() {
    console.log('🧬 Starting DNA Navigation testing suite...');
    this.runAllTests();
  }

  async runAllTests() {
    await this.testDNANavigationInitialization();
    await this.testScrollTracking();
    await this.testInteractivity();
    await this.testResponsiveDesign();
    await this.testAccessibility();
    await this.testPerformance();
    this.generateReport();
  }

  async testDNANavigationInitialization() {
    console.log('🧬 Testing DNA Navigation initialization...');
    
    // Test DNA Navigation instance
    this.addTestResult(
      'DNA Navigation Instance',
      typeof window.dnaNavigation !== 'undefined',
      'DNA Navigation should be initialized'
    );

    // Test container creation
    const container = document.getElementById('dna-navigation');
    this.addTestResult(
      'DNA Container',
      !!container,
      'DNA navigation container should exist'
    );

    if (container) {
      // Test container positioning
      const styles = getComputedStyle(container);
      this.addTestResult(
        'Container Positioning',
        styles.position === 'fixed',
        'Container should be fixed positioned'
      );

      // Test glassmorphism effects
      const hasBackdropFilter = styles.backdropFilter !== 'none' || styles.webkitBackdropFilter !== 'none';
      this.addTestResult(
        'Glassmorphism Effects',
        hasBackdropFilter || styles.background.includes('rgba'),
        'Container should have glassmorphism effects'
      );
    }

    // Test toggle button
    const toggleButton = document.getElementById('dna-nav-toggle');
    this.addTestResult(
      'Toggle Button',
      !!toggleButton,
      'DNA navigation toggle button should exist'
    );
  }

  async testScrollTracking() {
    console.log('📜 Testing scroll tracking functionality...');
    
    if (!window.dnaNavigation) {
      this.addTestResult('Scroll Tracking', false, 'DNA Navigation not initialized');
      return;
    }

    // Test sections configuration
    const sections = window.dnaNavigation.sections;
    this.addTestResult(
      'Sections Configuration',
      sections && sections.length === 6,
      `Should have 6 sections, found ${sections ? sections.length : 0}`
    );

    // Test section elements exist
    let sectionsFound = 0;
    if (sections) {
      sections.forEach(section => {
        const element = document.getElementById(section.id);
        if (element) sectionsFound++;
      });
    }

    this.addTestResult(
      'Section Elements',
      sectionsFound === 6,
      `${sectionsFound}/6 section elements found in DOM`
    );

    // Test scroll progress tracking
    const hasScrollTracking = typeof window.dnaNavigation.updateSectionProgress === 'function';
    this.addTestResult(
      'Scroll Progress Tracking',
      hasScrollTracking,
      'DNA Navigation should have scroll progress tracking'
    );
  }

  async testInteractivity() {
    console.log('🖱️ Testing DNA Navigation interactivity...');
    
    if (!window.dnaNavigation) {
      this.addTestResult('Navigation Interactivity', false, 'DNA Navigation not initialized');
      return;
    }

    // Test navigation methods
    const hasNavigateMethod = typeof window.dnaNavigation.navigateToSection === 'function';
    this.addTestResult(
      'Navigation Method',
      hasNavigateMethod,
      'Should have navigateToSection method'
    );

    // Test hover functionality
    const hasHoverMethod = typeof window.dnaNavigation.handleHover === 'function';
    this.addTestResult(
      'Hover Functionality',
      hasHoverMethod,
      'Should have hover handling method'
    );

    // Test tooltip system
    const hasTooltipMethod = typeof window.dnaNavigation.showTooltip === 'function';
    this.addTestResult(
      'Tooltip System',
      hasTooltipMethod,
      'Should have tooltip functionality'
    );

    // Test mobile toggle
    const hasToggleMethod = typeof window.dnaNavigation.toggleNavigation === 'function';
    this.addTestResult(
      'Mobile Toggle',
      hasToggleMethod,
      'Should have mobile toggle functionality'
    );
  }

  async testResponsiveDesign() {
    console.log('📱 Testing responsive design...');
    
    const container = document.getElementById('dna-navigation');
    if (!container) {
      this.addTestResult('Responsive Design', false, 'Container not found');
      return;
    }

    // Test responsive breakpoints
    const originalWidth = window.innerWidth;
    
    // Simulate mobile
    Object.defineProperty(window, 'innerWidth', { value: 600, configurable: true });
    const isMobileDetected = window.dnaNavigation ? window.dnaNavigation.isMobile : true;
    
    // Simulate tablet
    Object.defineProperty(window, 'innerWidth', { value: 800, configurable: true });
    const isTabletDetected = window.dnaNavigation ? window.dnaNavigation.isTablet : true;
    
    // Restore original width
    Object.defineProperty(window, 'innerWidth', { value: originalWidth, configurable: true });

    this.addTestResult(
      'Mobile Detection',
      isMobileDetected,
      'Should detect mobile viewport'
    );

    this.addTestResult(
      'Tablet Detection',
      isTabletDetected,
      'Should detect tablet viewport'
    );

    // Test toggle button visibility on mobile
    const toggleButton = document.getElementById('dna-nav-toggle');
    if (toggleButton) {
      const toggleStyles = getComputedStyle(toggleButton);
      this.addTestResult(
        'Toggle Button Responsive',
        toggleStyles.display !== 'none' || window.innerWidth >= 768,
        'Toggle button should be responsive to screen size'
      );
    }
  }

  async testAccessibility() {
    console.log('♿ Testing accessibility features...');
    
    // Test ARIA labels
    const toggleButton = document.getElementById('dna-nav-toggle');
    if (toggleButton) {
      const hasAriaLabel = toggleButton.hasAttribute('aria-label');
      this.addTestResult(
        'ARIA Labels',
        hasAriaLabel,
        'Toggle button should have aria-label'
      );
    }

    // Test keyboard navigation
    const container = document.getElementById('dna-navigation');
    if (container) {
      const isFocusable = container.tabIndex >= 0 || container.querySelector('[tabindex]');
      this.addTestResult(
        'Keyboard Navigation',
        !!isFocusable,
        'DNA navigation should be keyboard accessible'
      );
    }

    // Test reduced motion support
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    this.addTestResult(
      'Reduced Motion Support',
      true, // CSS handles this automatically
      'Should respect prefers-reduced-motion setting'
    );

    // Test high contrast support
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
    this.addTestResult(
      'High Contrast Support',
      true, // CSS handles this automatically
      'Should support high contrast mode'
    );
  }

  async testPerformance() {
    console.log('⚡ Testing performance...');
    
    if (!window.dnaNavigation) {
      this.addTestResult('Performance', false, 'DNA Navigation not initialized');
      return;
    }

    // Test WebGL support detection
    const hasWebGLCheck = typeof window.dnaNavigation.checkWebGLSupport === 'function';
    this.addTestResult(
      'WebGL Detection',
      hasWebGLCheck,
      'Should detect WebGL support'
    );

    // Test fallback system
    const hasFallback = typeof window.dnaNavigation.createCSSFallback === 'function';
    this.addTestResult(
      'Fallback System',
      hasFallback,
      'Should have CSS fallback for unsupported browsers'
    );

    // Test animation performance
    const hasAnimationLoop = typeof window.dnaNavigation.animate === 'function';
    this.addTestResult(
      'Animation Loop',
      hasAnimationLoop,
      'Should have optimized animation loop'
    );

    // Test memory management
    const hasDispose = typeof window.dnaNavigation.dispose === 'function';
    this.addTestResult(
      'Memory Management',
      hasDispose,
      'Should have dispose method for cleanup'
    );

    // Test container containment
    const container = document.getElementById('dna-navigation');
    if (container) {
      const styles = getComputedStyle(container);
      const hasContainment = styles.contain !== 'none';
      this.addTestResult(
        'CSS Containment',
        hasContainment,
        'Container should use CSS containment for performance'
      );
    }
  }

  addTestResult(testName, passed, description) {
    this.testResults.push({
      name: testName,
      passed,
      description,
      timestamp: new Date().toISOString()
    });
  }

  generateReport() {
    const passedTests = this.testResults.filter(test => test.passed).length;
    const totalTests = this.testResults.length;
    const passRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.group('🧬 DNA Navigation Testing Report');
    console.log(`Overall: ${passedTests}/${totalTests} tests passed (${passRate}%)`);
    
    const categories = [
      'Initialization',
      'Scroll Tracking', 
      'Interactivity',
      'Responsive Design',
      'Accessibility',
      'Performance'
    ];

    categories.forEach(category => {
      const categoryTests = this.testResults.filter(test => 
        test.name.toLowerCase().includes(category.toLowerCase().replace(' ', ''))
      );
      
      if (categoryTests.length > 0) {
        const categoryPassed = categoryTests.filter(test => test.passed).length;
        console.group(`📋 ${category} (${categoryPassed}/${categoryTests.length})`);
        categoryTests.forEach(test => {
          const icon = test.passed ? '✅' : '❌';
          console.log(`${icon} ${test.name}: ${test.description}`);
        });
        console.groupEnd();
      }
    });
    
    console.groupEnd();
    
    // Store results globally
    window.dnaNavigationTestResults = {
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: totalTests - passedTests,
        passRate: passRate + '%'
      },
      details: this.testResults
    };
    
    // Show visual indicator
    this.showDNATestIndicator(passRate);
    
    return this.testResults;
  }

  showDNATestIndicator(passRate) {
    const indicator = document.createElement('div');
    indicator.id = 'dna-test-indicator';
    indicator.style.cssText = `
      position: fixed;
      top: 200px;
      right: 10px;
      z-index: 10000;
      padding: 12px 16px;
      border-radius: 12px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      ${passRate >= 95 ? 'background: rgba(40, 167, 69, 0.8);' : 
        passRate >= 80 ? 'background: rgba(255, 193, 7, 0.8); color: #000;' : 
        'background: rgba(220, 53, 69, 0.8);'}
    `;
    
    indicator.innerHTML = `
      <div>🧬 DNA: ${passRate}%</div>
      <div style="font-size: 10px; opacity: 0.8;">Click for details</div>
    `;
    
    indicator.addEventListener('click', () => {
      console.log('DNA Navigation Test Results:', window.dnaNavigationTestResults);
    });
    
    document.body.appendChild(indicator);
    
    // Auto-hide after 15 seconds
    setTimeout(() => {
      if (indicator.parentNode) {
        indicator.style.opacity = '0';
        setTimeout(() => indicator.remove(), 300);
      }
    }, 15000);
  }
}

// Auto-run DNA navigation tests when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Wait for DNA navigation to initialize
  setTimeout(() => {
    new DNANavigationTester();
  }, 5000);
});

// Manual test trigger
window.testDNANavigation = function() {
  new DNANavigationTester();
};

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DNANavigationTester;
}
