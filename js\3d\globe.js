// Advanced Three.js Globe Component with Performance Optimizations
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer';

export class GlobeVisualization {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true,
            powerPreference: 'high-performance'
        });
        
        // Performance optimizations
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.useObjectPooling = true;
        this.objectPool = new Map();
        
        // LOD System setup
        this.setupLODSystem();
        
        // Progressive loading
        this.loadManager = new THREE.LoadingManager();
        this.setupProgressiveLoading();
        
        // Location marker for FEFU (Vladivostok)
        this.fefuCoordinates = {
            latitude: 43.1332,
            longitude: 131.9113
        };
        
        this.init();
    }
    
    setupLODSystem() {
        this.lodSystem = {
            high: { distance: 5, vertices: 128 },
            medium: { distance: 10, vertices: 64 },
            low: { distance: 20, vertices: 32 }
        };
    }
    
    setupProgressiveLoading() {
        this.loadManager.onProgress = (url, loaded, total) => {
            const progress = (loaded / total * 100).toFixed(2);
            this.updateLoadingProgress(progress);
        };
    }
    
    async init() {
        this.setupScene();
        await this.loadTextures();
        this.createGlobe();
        this.createLocationMarker();
        this.setupInteraction();
        this.animate();
    }
    
    setupScene() {
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.container.appendChild(this.renderer.domElement);
        
        // Ambient light with medical-grade color
        const ambientLight = new THREE.AmbientLight(0x2563eb, 0.5);
        this.scene.add(ambientLight);
        
        // Directional light for depth
        const directionalLight = new THREE.DirectionalLight(0x0891b2, 1);
        directionalLight.position.set(5, 3, 5);
        this.scene.add(directionalLight);
        
        this.camera.position.z = 5;
    }
    
    async loadTextures() {
        const textureLoader = new THREE.TextureLoader(this.loadManager);
        this.earthTexture = await textureLoader.loadAsync('/assets/earth_texture.jpg');
        this.earthTexture.minFilter = THREE.LinearMipMapLinearFilter;
    }
    
    createGlobe() {
        const geometry = new THREE.SphereGeometry(2, 64, 64);
        const material = new THREE.MeshPhongMaterial({
            map: this.earthTexture,
            bumpScale: 0.05,
            specular: new THREE.Color(0x2563eb),
            shininess: 5
        });
        
        this.globe = new THREE.Mesh(geometry, material);
        this.scene.add(this.globe);
    }
    
    createLocationMarker() {
        const markerGeometry = new THREE.SphereGeometry(0.05, 16, 16);
        const markerMaterial = new THREE.MeshBasicMaterial({ 
            color: 0xff0000,
            transparent: true,
            opacity: 0.8
        });
        
        this.locationMarker = new THREE.Mesh(markerGeometry, markerMaterial);
        this.updateMarkerPosition();
        this.scene.add(this.locationMarker);
        
        // Pulsing animation
        this.pulseMarker();
    }
    
    updateMarkerPosition() {
        const phi = (90 - this.fefuCoordinates.latitude) * (Math.PI / 180);
        const theta = (this.fefuCoordinates.longitude + 180) * (Math.PI / 180);
        
        this.locationMarker.position.x = -(Math.sin(phi) * Math.cos(theta)) * 2;
        this.locationMarker.position.y = Math.cos(phi) * 2;
        this.locationMarker.position.z = (Math.sin(phi) * Math.sin(theta)) * 2;
    }
    
    pulseMarker() {
        const pulse = () => {
            gsap.to(this.locationMarker.scale, {
                x: 1.5,
                y: 1.5,
                z: 1.5,
                duration: 1,
                ease: "power1.inOut",
                yoyo: true,
                repeat: -1
            });
        };
        
        pulse();
    }
    
    setupInteraction() {
        let isDragging = false;
        let previousMousePosition = { x: 0, y: 0 };
        
        this.container.addEventListener('mousedown', (e) => {
            isDragging = true;
            previousMousePosition = {
                x: e.clientX,
                y: e.clientY
            };
        });
        
        this.container.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            
            const deltaMove = {
                x: e.clientX - previousMousePosition.x,
                y: e.clientY - previousMousePosition.y
            };
            
            this.globe.rotation.y += deltaMove.x * 0.005;
            this.globe.rotation.x += deltaMove.y * 0.005;
            
            previousMousePosition = {
                x: e.clientX,
                y: e.clientY
            };
        });
        
        this.container.addEventListener('mouseup', () => {
            isDragging = false;
        });
        
        // Touch events
        this.container.addEventListener('touchstart', (e) => {
            isDragging = true;
            previousMousePosition = {
                x: e.touches[0].clientX,
                y: e.touches[0].clientY
            };
        });
        
        this.container.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            
            const deltaMove = {
                x: e.touches[0].clientX - previousMousePosition.x,
                y: e.touches[0].clientY - previousMousePosition.y
            };
            
            this.globe.rotation.y += deltaMove.x * 0.005;
            this.globe.rotation.x += deltaMove.y * 0.005;
            
            previousMousePosition = {
                x: e.touches[0].clientX,
                y: e.touches[0].clientY
            };
        });
        
        this.container.addEventListener('touchend', () => {
            isDragging = false;
        });
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        if (!this.isDragging) {
            this.globe.rotation.y += 0.001;
        }
        
        this.updateMarkerPosition();
        this.renderer.render(this.scene, this.camera);
    }
    
    updateLoadingProgress(progress) {
        const loadingEl = this.container.querySelector('.component-loading');
        if (loadingEl) {
            if (progress >= 100) {
                loadingEl.style.display = 'none';
            } else {
                loadingEl.querySelector('.loading-spinner').style.setProperty('--progress', `${progress}%`);
            }
        }
    }
    
    resize() {
        this.camera.aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    }
}
