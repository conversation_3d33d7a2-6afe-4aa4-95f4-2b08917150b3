/* Premium Effects and Animations */
:root {
    /* Medical Color Palette */
    --color-primary: #2563eb;
    --color-secondary: #0891b2;
    --color-accent: #06b6d4;
    --color-background: #ffffff;
    --color-text: #1e293b;
    
    /* Glassmorphism */
    --glass-background: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-blur: 20px;
    
    /* Animations */
    --transition-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --animation-duration: 0.3s;
}

/* Advanced Glassmorphism */
.glass-effect {
    background: var(--glass-background);
    backdrop-filter: blur(var(--glass-blur));
    -webkit-backdrop-filter: blur(var(--glass-blur));
    border: 1px solid var(--glass-border);
    border-radius: 24px;
    transition: all var(--animation-duration) var(--transition-smooth);
}

.glass-effect:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Magnetic Hover Effect */
.magnetic-hover {
    transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    transform-style: preserve-3d;
    perspective: 1000px;
}

/* Premium Button Effects */
.premium-button {
    position: relative;
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    border: none;
    padding: 16px 32px;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    overflow: hidden;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
}

.premium-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
}

.premium-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.2);
}

.premium-button:hover::before {
    animation: shimmer 1.5s infinite;
}

/* Scroll-Triggered Animations */
.reveal-up {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.reveal-up.active {
    opacity: 1;
    transform: translateY(0);
}

.reveal-scale {
    opacity: 0;
    transform: scale(0.95);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.reveal-scale.active {
    opacity: 1;
    transform: scale(1);
}

/* Stagger Animation System */
.stagger-item {
    opacity: 0;
    transform: translateY(20px);
}

.stagger-item.visible {
    animation: staggerFadeIn 0.5s ease forwards;
}

/* Advanced Card Hover Effects */
.info-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-style: preserve-3d;
}

.info-card:hover {
    transform: translateY(-8px) rotateX(5deg);
}

/* Particle Background */
.particle-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
    @media (prefers-reduced-motion: reduce) {
        scroll-behavior: auto;
    }
}

/* Advanced Loading Animation */
.loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid rgba(37, 99, 235, 0.1);
    border-left-color: var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Keyframe Animations */
@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes staggerFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Fluid Typography */
@media (min-width: 320px) {
    :root {
        font-size: calc(16px + (24 - 16) * ((100vw - 320px) / (1920 - 320)));
    }
}

/* Performance Optimizations */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    backface-visibility: hidden;
    transform: translateZ(0);
    will-change: transform;
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

.focus-visible:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}
