/* ===== SIMPLE DOT NAVIGATION ===== */
/* Clean, minimal navigation with dots */

/* ===== DOT NAVIGATION CONTAINER ===== */
.dot-navigation {
  position: fixed;
  top: 50%;
  right: 30px;
  transform: translateY(-50%);
  z-index: 1000;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: 25px;
  padding: 15px 8px;
  box-shadow: var(--shadow-premium);
  transition: all 0.3s var(--ease-premium);
}

.dot-navigation:hover {
  background: var(--glass-bg-strong);
  transform: translateY(-50%) scale(1.02);
  box-shadow: var(--shadow-multi-layer);
}

.dot-nav-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

/* ===== DOT NAVIGATION ITEMS ===== */
.dot-nav-item {
  position: relative;
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: all 0.3s var(--ease-premium);
  cursor: pointer;
  padding: 8px;
  border-radius: 12px;
  min-height: 44px; /* Accessibility touch target */
  min-width: 44px;
  justify-content: center;
}

.dot-nav-item:hover {
  background: rgba(37, 99, 235, 0.1);
  transform: scale(1.1);
}

.dot-nav-item.active {
  background: rgba(37, 99, 235, 0.15);
}

/* ===== DOTS ===== */
.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  border: 2px solid rgba(37, 99, 235, 0.3);
  transition: all 0.3s var(--ease-premium);
  position: relative;
  z-index: 1;
}

.dot-nav-item:hover .dot {
  background: var(--primary-500);
  border-color: var(--primary-600);
  box-shadow: 0 0 12px rgba(37, 99, 235, 0.4);
  transform: scale(1.2);
}

.dot-nav-item.active .dot {
  background: var(--primary-600);
  border-color: var(--primary-700);
  box-shadow: 0 0 16px rgba(37, 99, 235, 0.6);
  transform: scale(1.3);
}

/* ===== DOT LABELS ===== */
.dot-label {
  position: absolute;
  right: 100%;
  margin-right: 15px;
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  color: var(--color-text);
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: var(--font-medium);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transform: translateX(10px);
  transition: all 0.3s var(--ease-premium);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-premium);
  z-index: 2;
}

.dot-label::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 100%;
  transform: translateY(-50%);
  border: 6px solid transparent;
  border-left-color: var(--glass-border);
}

.dot-nav-item:hover .dot-label {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.dot-nav-item.active .dot-label {
  background: rgba(37, 99, 235, 0.1);
  color: var(--primary-600);
  border-color: rgba(37, 99, 235, 0.3);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet adjustments */
@media (max-width: 1024px) {
  .dot-navigation {
    right: 20px;
    padding: 12px 6px;
  }
  
  .dot-nav-container {
    gap: 10px;
  }
  
  .dot {
    width: 10px;
    height: 10px;
  }
  
  .dot-label {
    font-size: 0.75rem;
    padding: 5px 10px;
  }
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .dot-navigation {
    right: 15px;
    padding: 10px 5px;
    border-radius: 20px;
  }
  
  .dot-nav-container {
    gap: 8px;
  }
  
  .dot-nav-item {
    padding: 6px;
    min-height: 40px;
    min-width: 40px;
  }
  
  .dot {
    width: 8px;
    height: 8px;
  }
  
  .dot-label {
    display: none; /* Hide labels on mobile to save space */
  }
}

/* Very small mobile */
@media (max-width: 480px) {
  .dot-navigation {
    right: 10px;
    padding: 8px 4px;
    border-radius: 18px;
  }
  
  .dot-nav-container {
    gap: 6px;
  }
  
  .dot-nav-item {
    padding: 4px;
    min-height: 36px;
    min-width: 36px;
  }
  
  .dot {
    width: 6px;
    height: 6px;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* Focus states for keyboard navigation */
.dot-nav-item:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  background: rgba(37, 99, 235, 0.1);
}

.dot-nav-item:focus .dot {
  background: var(--primary-500);
  border-color: var(--primary-600);
  box-shadow: 0 0 12px rgba(37, 99, 235, 0.4);
}

.dot-nav-item:focus .dot-label {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .dot-navigation {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #000;
  }
  
  .dot {
    background: #000;
    border-color: #000;
  }
  
  .dot-nav-item.active .dot {
    background: var(--primary-600);
    border-color: var(--primary-600);
  }
  
  .dot-label {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #000;
    color: #000;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .dot-navigation,
  .dot-nav-item,
  .dot,
  .dot-label {
    transition: none !important;
    animation: none !important;
  }
  
  .dot-nav-item:hover {
    transform: none !important;
  }
  
  .dot-navigation:hover {
    transform: translateY(-50%) !important;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .dot-navigation {
    display: none !important;
  }
}

/* ===== INTEGRATION WITH EXISTING COMPONENTS ===== */

/* Ensure proper z-index layering */
.dot-navigation {
  z-index: 999; /* Below DNA navigation but above content */
}

/* Coordinate with DNA navigation positioning */
@media (min-width: 769px) {
  .dot-navigation {
    right: 120px; /* Leave space for DNA navigation */
  }
}

/* Adjust for mobile when DNA navigation is visible */
@media (max-width: 768px) {
  .dot-navigation {
    right: 15px; /* DNA navigation is hidden on mobile */
  }
}

/* ===== SMOOTH ANIMATIONS ===== */
@keyframes dotPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.dot-nav-item.active .dot {
  animation: dotPulse 2s ease-in-out infinite;
}

/* ===== GLASSMORPHISM CONSISTENCY ===== */
.dot-navigation {
  /* Use existing glassmorphism variables for consistency */
  background: var(--glass-bg, rgba(255, 255, 255, 0.1));
  backdrop-filter: var(--glass-backdrop, blur(20px));
  -webkit-backdrop-filter: var(--glass-backdrop, blur(20px));
  border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.2));
  box-shadow: var(--shadow-premium, 0 8px 32px rgba(0, 0, 0, 0.1));
}

.dot-label {
  background: var(--glass-bg-strong, rgba(255, 255, 255, 0.2));
  backdrop-filter: var(--glass-backdrop, blur(20px));
  -webkit-backdrop-filter: var(--glass-backdrop, blur(20px));
  border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.2));
  box-shadow: var(--shadow-premium, 0 8px 32px rgba(0, 0, 0, 0.1));
}

/* ===== PERFORMANCE OPTIMIZATION ===== */
.dot-navigation {
  will-change: transform;
  contain: layout style paint;
}

.dot-nav-item {
  will-change: transform, background-color;
}

.dot {
  will-change: transform, background-color, box-shadow;
}

.dot-label {
  will-change: opacity, visibility, transform;
}

/* ===== REMOVE MAIN CONTENT PADDING ===== */
/* Since we removed the fixed navbar, remove the padding offset */
#main-content {
  padding-top: 0 !important;
}

.hero-section {
  min-height: 100vh !important;
}

section[id] {
  scroll-margin-top: 20px !important;
}
