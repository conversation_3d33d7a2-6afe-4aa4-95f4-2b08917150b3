/* ===== STAGGER ITEMS TESTING SUITE ===== */
/* Comprehensive testing for all stagger item animations */

class StaggerItemsTester {
  constructor() {
    this.testResults = [];
    this.staggerSections = [];
    this.init();
  }

  init() {
    console.log('🎭 Starting stagger items testing suite...');
    this.identifyStaggerSections();
    this.testAllStaggerItems();
    this.generateReport();
  }

  identifyStaggerSections() {
    console.log('🔍 Identifying all sections with stagger items...');
    
    // Define expected stagger sections
    this.staggerSections = [
      {
        name: 'Country Cards',
        section: 'Career Pathways',
        containerSelector: '.countries-grid',
        itemSelector: '.country-card.stagger-item',
        expectedCount: 4,
        hasRevealClass: true
      },
      {
        name: 'Facility Cards',
        section: 'Campus Life',
        containerSelector: '.facilities-grid',
        itemSelector: '.facility-card.stagger-item',
        expectedCount: 4,
        hasRevealClass: true
      },
      {
        name: 'Safety Cards',
        section: 'Safety & Support',
        containerSelector: '.safety-grid',
        itemSelector: '.safety-card.stagger-item',
        expectedCount: 6,
        hasRevealClass: true
      },
      {
        name: 'About Cards',
        section: 'About FEFU',
        containerSelector: '.about-grid',
        itemSelector: '.about-card.stagger-item',
        expectedCount: 3,
        hasRevealClass: true
      },
      {
        name: 'Year Cards',
        section: 'Curriculum',
        containerSelector: '.curriculum-timeline',
        itemSelector: '.year-card.stagger-item',
        expectedCount: 6,
        hasRevealClass: true
      },
      {
        name: 'Hospital Cards',
        section: 'Clinical Training',
        containerSelector: '.hospitals-grid',
        itemSelector: '.hospital-card.stagger-item',
        expectedCount: 3,
        hasRevealClass: true
      }
    ];
  }

  testAllStaggerItems() {
    console.log('🧪 Testing all stagger item implementations...');
    
    this.staggerSections.forEach(sectionConfig => {
      this.testStaggerSection(sectionConfig);
    });

    // Test for orphaned stagger items
    this.testOrphanedStaggerItems();
  }

  testStaggerSection(config) {
    const container = document.querySelector(config.containerSelector);
    const items = document.querySelectorAll(config.itemSelector);
    
    // Test container exists
    if (!container) {
      this.addTestResult(
        `${config.name} Container`,
        false,
        `Container not found: ${config.containerSelector}`,
        config.section
      );
      return;
    }

    // Test reveal class on container
    const hasRevealClass = container.classList.contains('reveal-up') ||
                          container.classList.contains('reveal-left') ||
                          container.classList.contains('reveal-right') ||
                          container.classList.contains('reveal-scale');
    
    this.addTestResult(
      `${config.name} Reveal Trigger`,
      hasRevealClass,
      hasRevealClass ? 'Container has reveal class' : 'Container missing reveal class',
      config.section
    );

    // Test item count
    const correctCount = items.length === config.expectedCount;
    this.addTestResult(
      `${config.name} Count`,
      correctCount,
      `Found ${items.length}/${config.expectedCount} items`,
      config.section
    );

    // Test individual items
    let visibleItems = 0;
    let animatedItems = 0;
    
    items.forEach((item, index) => {
      const styles = getComputedStyle(item);
      const isVisible = styles.opacity !== '0';
      const isAnimated = item.classList.contains('animate');
      
      if (isVisible) visibleItems++;
      if (isAnimated) animatedItems++;
    });

    // Test visibility
    this.addTestResult(
      `${config.name} Visibility`,
      visibleItems > 0,
      `${visibleItems}/${items.length} items visible`,
      config.section
    );

    // Test animation state
    this.addTestResult(
      `${config.name} Animation`,
      animatedItems >= visibleItems,
      `${animatedItems}/${items.length} items animated`,
      config.section
    );

    // Test stagger timing
    this.testStaggerTiming(config, items);
  }

  testStaggerTiming(config, items) {
    if (items.length === 0) return;

    // Check if items have proper stagger delay classes or inline delays
    let hasStaggerTiming = false;
    
    items.forEach((item, index) => {
      const styles = getComputedStyle(item);
      const transitionDelay = styles.transitionDelay;
      
      if (transitionDelay && transitionDelay !== '0s') {
        hasStaggerTiming = true;
      }
    });

    this.addTestResult(
      `${config.name} Stagger Timing`,
      hasStaggerTiming,
      hasStaggerTiming ? 'Items have stagger delays' : 'No stagger timing detected',
      config.section
    );
  }

  testOrphanedStaggerItems() {
    console.log('🔍 Testing for orphaned stagger items...');
    
    const allStaggerItems = document.querySelectorAll('.stagger-item');
    const orphanedItems = [];
    
    allStaggerItems.forEach(item => {
      let hasRevealParent = false;
      let currentElement = item.parentElement;
      
      while (currentElement && currentElement !== document.body) {
        if (currentElement.classList.contains('reveal-up') || 
            currentElement.classList.contains('reveal-left') || 
            currentElement.classList.contains('reveal-right') || 
            currentElement.classList.contains('reveal-scale')) {
          hasRevealParent = true;
          break;
        }
        currentElement = currentElement.parentElement;
      }
      
      if (!hasRevealParent) {
        orphanedItems.push(item);
      }
    });

    this.addTestResult(
      'Orphaned Stagger Items',
      orphanedItems.length === 0,
      orphanedItems.length === 0 ? 'No orphaned items found' : `${orphanedItems.length} orphaned items detected`,
      'System'
    );

    if (orphanedItems.length > 0) {
      console.warn('⚠️ Orphaned stagger items found:', orphanedItems);
    }
  }

  addTestResult(testName, passed, description, section) {
    this.testResults.push({
      name: testName,
      section,
      passed,
      description,
      timestamp: new Date().toISOString()
    });
  }

  generateReport() {
    const passedTests = this.testResults.filter(test => test.passed).length;
    const totalTests = this.testResults.length;
    const passRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.group('🎭 Stagger Items Testing Report');
    console.log(`Overall: ${passedTests}/${totalTests} stagger tests passed (${passRate}%)`);
    
    // Group results by section
    const sections = [...new Set(this.testResults.map(test => test.section))];
    
    sections.forEach(section => {
      const sectionTests = this.testResults.filter(test => test.section === section);
      const sectionPassed = sectionTests.filter(test => test.passed).length;
      
      console.group(`📋 ${section} Section (${sectionPassed}/${sectionTests.length})`);
      sectionTests.forEach(test => {
        const icon = test.passed ? '✅' : '❌';
        console.log(`${icon} ${test.name}: ${test.description}`);
      });
      console.groupEnd();
    });
    
    console.groupEnd();
    
    // Store results globally
    window.staggerTestResults = {
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: totalTests - passedTests,
        passRate: passRate + '%'
      },
      details: this.testResults,
      sections: sections.map(section => {
        const sectionTests = this.testResults.filter(test => test.section === section);
        return {
          name: section,
          total: sectionTests.length,
          passed: sectionTests.filter(test => test.passed).length,
          tests: sectionTests
        };
      })
    };
    
    // Show visual indicator
    this.showStaggerIndicator(passRate);
    
    return this.testResults;
  }

  showStaggerIndicator(passRate) {
    const indicator = document.createElement('div');
    indicator.id = 'stagger-indicator';
    indicator.style.cssText = `
      position: fixed;
      top: 150px;
      right: 10px;
      z-index: 10000;
      padding: 12px 16px;
      border-radius: 12px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      ${passRate >= 95 ? 'background: rgba(40, 167, 69, 0.8);' : 
        passRate >= 80 ? 'background: rgba(255, 193, 7, 0.8); color: #000;' : 
        'background: rgba(220, 53, 69, 0.8);'}
    `;
    
    indicator.innerHTML = `
      <div>Stagger: ${passRate}%</div>
      <div style="font-size: 10px; opacity: 0.8;">Click for details</div>
    `;
    
    indicator.addEventListener('click', () => {
      console.log('Stagger Test Results:', window.staggerTestResults);
    });
    
    document.body.appendChild(indicator);
    
    // Auto-hide after 15 seconds
    setTimeout(() => {
      if (indicator.parentNode) {
        indicator.style.opacity = '0';
        setTimeout(() => indicator.remove(), 300);
      }
    }, 15000);
  }

  // Manual trigger to animate all stagger items (for debugging)
  static animateAllStaggerItems() {
    console.log('🔧 Manually animating all stagger items...');
    const staggerItems = document.querySelectorAll('.stagger-item:not(.animate)');
    
    staggerItems.forEach((item, index) => {
      setTimeout(() => {
        item.classList.add('animate');
        console.log(`Animated stagger item ${index + 1}/${staggerItems.length}`);
      }, index * 100);
    });
    
    return staggerItems.length;
  }
}

// Auto-run stagger tests when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Wait for other components to initialize
  setTimeout(() => {
    new StaggerItemsTester();
  }, 4000);
});

// Manual test triggers
window.testStaggerItems = function() {
  new StaggerItemsTester();
};

window.animateAllStaggerItems = function() {
  return StaggerItemsTester.animateAllStaggerItems();
};

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = StaggerItemsTester;
}
